import subprocess
import os

def split_video_ffmpeg(input_path, output_dir, chunk_duration=10):
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    # FFmpeg command to split video into chunks by time
    cmd = [
        'ffmpeg',
        '-i', input_path,
        '-c:v', 'copy',                        # Copy codec to avoid re-encoding (fast)
        '-map', '0',                        # Map all streams
        '-f', 'segment',                   # Segment format
        '-segment_time', str(chunk_duration),
        '-reset_timestamps', '1',
        os.path.join(output_dir, 'chunk_%03d.mp4')
    ]
    subprocess.run(cmd, check=True)

# Example usage:
input_video = 'cat-on-boat.mp4'
output_dir = 'video_chunks'
chunk_sec = 10  # chunk length in seconds

split_video_ffmpeg(input_video, output_dir, chunk_sec)
print(f"Video split into {chunk_sec}-second chunks in folder '{output_dir}'")
