#!/usr/bin/env python3
"""
Example usage of the Video Analysis Database
Demonstrates how to store YOLO results and dense captions
"""

import json
import os
import sys
from pathlib import Path
from datetime import datetime

# Add the parent directory to the path to import our modules
sys.path.append(str(Path(__file__).parent.parent))

from database.video_analysis_db import (
    VideoAnalysisDB, VideoInfo, VideoChunkInfo, AnalysisRunInfo
)


def load_yolo_json(json_path: str) -> dict:
    """Load YOLO results from JSON file"""
    with open(json_path, 'r') as f:
        return json.load(f)


def extract_chunk_info_from_path(chunk_path: str) -> tuple:
    """Extract chunk index and timing info from chunk filename"""
    # Assuming format like "chunk_000.mp4" for 10-second chunks
    filename = Path(chunk_path).stem
    if filename.startswith('chunk_'):
        chunk_index = int(filename.split('_')[1])
        # Assuming 10-second chunks
        start_time = chunk_index * 10.0
        end_time = start_time + 10.0
        return chunk_index, start_time, end_time
    return 0, 0.0, 10.0


def main():
    """Example workflow for storing video analysis data"""
    
    # Database connection string - adjust for your setup
    DB_CONNECTION = "postgresql://username:password@localhost:5432/video_analysis"
    
    # Example paths - adjust for your data
    ORIGINAL_VIDEO_PATH = "cat-on-boat.mp4"
    CHUNK_DIRECTORY = "video_chunks"
    YOLO_RESULTS_PATH = "output/chunk_000_track_results.json"
    
    try:
        with VideoAnalysisDB(DB_CONNECTION) as db:
            print("Connected to database successfully!")
            
            # =============================================
            # 1. Store original video information
            # =============================================
            print("\n1. Storing original video information...")
            
            video_info = VideoInfo(
                filename="cat-on-boat.mp4",
                file_path=ORIGINAL_VIDEO_PATH,
                file_size=os.path.getsize(ORIGINAL_VIDEO_PATH) if os.path.exists(ORIGINAL_VIDEO_PATH) else None,
                duration_seconds=30.0,  # Example duration
                fps=30.0,
                width=720,
                height=720,
                format="mp4",
                metadata={"source": "demo", "description": "Cat on boat video"}
            )
            
            video_id = db.insert_video(video_info)
            print(f"Stored video with ID: {video_id}")
            
            # =============================================
            # 2. Store video chunk information
            # =============================================
            print("\n2. Storing video chunk information...")
            
            chunk_path = "../video_chunks/chunk_000.mp4"
            chunk_index, start_time, end_time = extract_chunk_info_from_path(chunk_path)
            
            chunk_info = VideoChunkInfo(
                video_id=video_id,
                chunk_index=chunk_index,
                filename="chunk_000.mp4",
                file_path=chunk_path,
                start_time_seconds=start_time,
                end_time_seconds=end_time,
                duration_seconds=end_time - start_time,
                width=720,
                height=720,
                total_frames=320,  # From the example JSON
                fps=30.0
            )
            
            chunk_id = db.insert_video_chunk(chunk_info)
            print(f"Stored video chunk with ID: {chunk_id}")
            
            # =============================================
            # 3. Store YOLO analysis results
            # =============================================
            print("\n3. Storing YOLO analysis results...")
            
            # Load YOLO results
            if os.path.exists(YOLO_RESULTS_PATH):
                yolo_data = load_yolo_json(YOLO_RESULTS_PATH)
                print(f"Loaded YOLO results: {yolo_data['total_frames']} frames, {yolo_data['total_detections']} detections")
                
                # Create analysis run
                analysis_info = AnalysisRunInfo(
                    video_chunk_id=chunk_id,
                    model_name="yolov8x-worldv2",
                    model_version="v2",
                    task_type="track",
                    prompt_classes=["boat", "cat"],  # Example classes
                    parameters={"vid_stride": 5, "confidence_threshold": 0.25},
                    total_frames=yolo_data['total_frames'],
                    total_detections=yolo_data['total_detections'],
                    processing_time_seconds=15.2  # Example processing time
                )
                
                analysis_run_id = db.create_analysis_run(analysis_info)
                print(f"Created analysis run with ID: {analysis_run_id}")
                
                # Store detection results
                detections_stored = db.store_yolo_results(analysis_run_id, yolo_data)
                print(f"Stored {detections_stored} detections")
                
            else:
                print(f"YOLO results file not found: {YOLO_RESULTS_PATH}")
                # Create a dummy analysis run for demonstration
                analysis_info = AnalysisRunInfo(
                    video_chunk_id=chunk_id,
                    model_name="yolov8x-worldv2",
                    task_type="track",
                    prompt_classes=["boat", "cat"]
                )
                analysis_run_id = db.create_analysis_run(analysis_info)
                print(f"Created dummy analysis run with ID: {analysis_run_id}")
            
            # =============================================
            # 4. Store dense caption
            # =============================================
            print("\n4. Storing dense caption...")
            
            sample_caption = (
                "A cat is sitting on a boat in the water. The boat appears to be white and is floating "
                "on calm blue water. There are other boats visible in the background. The scene appears "
                "to be taken during daytime with good lighting conditions."
            )
            
            caption_id = db.store_dense_caption(
                video_chunk_id=chunk_id,
                caption_text=sample_caption,
                model_name="nvidia/vila",
                model_version="1.0",
                confidence_score=0.85,
                processing_time_seconds=2.3
            )
            print(f"Stored dense caption with ID: {caption_id}")
            
            # =============================================
            # 5. Query examples
            # =============================================
            print("\n5. Running example queries...")
            
            # Get video summary
            summary = db.get_video_summary(video_id)
            if summary:
                print(f"\nVideo Summary:")
                print(f"  Filename: {summary['video']['filename']}")
                print(f"  Chunks: {summary['chunk_count']}")
                print(f"  Captions: {summary['caption_count']}")
                print(f"  Analysis runs: {len(summary['analysis_summary'])}")
                for analysis in summary['analysis_summary']:
                    print(f"    - {analysis['model_name']} ({analysis['task_type']}): {analysis['total_detections']} detections")
            
            # Search captions
            search_results = db.search_captions("cat boat water")
            print(f"\nCaption search results for 'cat boat water': {len(search_results)} found")
            for result in search_results[:3]:  # Show top 3
                print(f"  - {result['filename']} chunk_{result['chunk_index']:03d}: {result['caption_text'][:100]}...")
            
            # Get detections by class
            if 'analysis_run_id' in locals():
                boat_detections = db.get_detections_by_class(analysis_run_id, "boat", min_confidence=0.5)
                print(f"\nHigh-confidence boat detections: {len(boat_detections)}")
                
                # Get track trajectory (if tracking was used)
                if boat_detections and boat_detections[0]['track_id']:
                    track_id = boat_detections[0]['track_id']
                    trajectory = db.get_track_trajectory(analysis_run_id, track_id)
                    print(f"Track {track_id} trajectory: {len(trajectory)} points")
                    if trajectory:
                        print(f"  First point: frame {trajectory[0]['frame_index']}, center ({trajectory[0]['center_x']:.1f}, {trajectory[0]['center_y']:.1f})")
                        print(f"  Last point: frame {trajectory[-1]['frame_index']}, center ({trajectory[-1]['center_x']:.1f}, {trajectory[-1]['center_y']:.1f})")
            
            print("\n✅ Example completed successfully!")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


def setup_database():
    """Helper function to set up the database schema"""
    print("To set up the database, run the following SQL files:")
    print("1. Create database: CREATE DATABASE video_analysis;")
    print("2. Run schema: psql -d video_analysis -f database/schema.sql")
    print("3. Optionally run sample queries: psql -d video_analysis -f database/sample_queries.sql")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "setup":
        setup_database()
    else:
        main()
