# Hybrid Video Analysis Database

A high-performance PostgreSQL database schema that combines **normalized structure** with **JSONB flexibility** for optimal video analysis data storage and querying.

## 🎯 Why Hybrid Approach?

The hybrid approach gives you the **best of both worlds**:

### ⚡ **Fast Queries** (Normalized Fields)
- **Extracted summary fields** for lightning-fast filtering
- **Array columns** for efficient class-based searches  
- **Proper indexing** on commonly queried fields
- **Materialized views** for complex aggregations

### 🔍 **Flexible Analysis** (JSONB Storage)
- **Complete YOLO JSON** preserved for detailed analysis
- **Path-based queries** for specific detection properties
- **GIN indexes** for efficient JSONB operations
- **Future-proof** for new model outputs

## 📊 Schema Overview

```
videos (metadata)
  ↓
video_chunks (segments)  
  ↓
analysis_results (HYBRID: extracted fields + full JSONB)
  ↓
dense_captions (AI descriptions)
```

### Key Tables

#### `analysis_results` (Hybrid Core)
```sql
-- Fast query fields (extracted automatically)
detected_classes TEXT[]           -- ['cat', 'boat', 'person']
avg_confidence DECIMAL(6,4)       -- 0.8542
total_detections INTEGER          -- 127
unique_track_count INTEGER        -- 8

-- Complete YOLO data (JSONB)
results_data JSONB               -- Full JSON from your pipeline
```

#### Automatic Field Extraction
Database triggers automatically extract summary fields from JSONB:
- **`detected_classes`** - Unique classes found
- **`avg_confidence`** - Average detection confidence  
- **`max_confidence`** - Highest confidence score
- **`unique_track_count`** - Number of tracked objects
- **`frames_with_detections`** - Frames containing objects

## 🚀 Performance Benefits

### Fast Queries (Using Extracted Fields)
```sql
-- Find videos with cats AND boats (milliseconds)
SELECT * FROM analysis_results 
WHERE detected_classes @> ARRAY['cat', 'boat']
  AND avg_confidence > 0.8;
```

### Detailed Analysis (Using JSONB)
```sql
-- Extract specific detection details (still fast with GIN indexes)
SELECT 
    (frame #>> '{frame_index}')::int as frame,
    detection #>> '{class_name}' as class,
    (detection #>> '{confidence}')::float as confidence
FROM analysis_results,
     jsonb_array_elements(results_data #> '{frames}') as frame,
     jsonb_array_elements(frame #> '{detections}') as detection
WHERE id = 'analysis-uuid'
  AND (detection #>> '{confidence}')::float > 0.9;
```

## 📝 Usage Examples

### Store YOLO Results
```python
from database.hybrid_video_db import HybridVideoAnalysisDB

with HybridVideoAnalysisDB(connection_string) as db:
    # Store directly from JSON file
    analysis_id = db.store_yolo_json_file(
        video_chunk_id=chunk_id,
        json_file_path="output/chunk_000_track_results.json",
        model_name="yolov8x-worldv2",
        task_type="track",
        prompt_classes=["boat", "cat"]
    )
    # Fields are extracted automatically!
```

### Fast Searches
```python
# Find videos with specific objects
videos = db.find_videos_with_classes(
    ["cat", "boat"], 
    min_confidence=0.8,
    require_all_classes=True
)

# Get high-confidence detections
detections = db.get_high_confidence_detections(
    analysis_id, 
    min_confidence=0.9,
    class_name="cat"
)

# Track object movement
trajectory = db.get_object_trajectory(analysis_id, track_id=1)
```

### Advanced Analytics
```python
# Object co-occurrence analysis
co_occurrences = db.find_co_occurring_objects(analysis_id)

# Model performance comparison  
performance = db.get_model_performance_comparison()

# Class statistics across all videos
stats = db.get_class_statistics()
```

## 🔧 Setup Instructions

### 1. Database Setup
```sql
-- Create database with extensions
CREATE DATABASE video_analysis;
\c video_analysis
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Run hybrid schema
\i database/hybrid_schema.sql
```

### 2. Python Setup
```bash
pip install psycopg2-binary
```

### 3. Configuration
```python
# Environment variables
export DB_HOST=localhost
export DB_PORT=5432  
export DB_NAME=video_analysis
export DB_USER=your_user
export DB_PASSWORD=your_password

# Or use connection string directly
DB_CONNECTION = "postgresql://user:pass@localhost:5432/video_analysis"
```

## 📈 Query Performance

### Indexed Operations (Fast)
- ✅ `detected_classes @> ARRAY['cat']` - Array containment
- ✅ `avg_confidence > 0.8` - Numeric comparison
- ✅ `model_name = 'yolov8x'` - Exact match
- ✅ `results_data @> '{"total_frames": 320}'` - JSONB containment

### JSONB Path Queries (Optimized)
- ✅ `results_data #> '{frames,0,detections}'` - Path extraction
- ✅ `results_data #>> '{total_detections}'` - Text extraction
- ✅ GIN indexes make these efficient

## 🎛️ Advanced Features

### Materialized Views
```sql
-- Pre-computed statistics for fast analytics
SELECT * FROM detection_stats 
WHERE class_name = 'cat'
ORDER BY class_avg_confidence DESC;

-- Refresh when needed
SELECT refresh_detection_stats();
```

### Full-Text Search on Captions
```sql
SELECT * FROM dense_captions 
WHERE caption_vector @@ plainto_tsquery('english', 'boat water ocean')
ORDER BY ts_rank(caption_vector, plainto_tsquery('english', 'boat water ocean')) DESC;
```

### Spatial Queries (Future)
```sql
-- PostGIS ready for bounding box spatial analysis
SELECT * FROM detections 
WHERE ST_Intersects(bbox_geometry, ST_MakeEnvelope(...));
```

## 🔍 Query Examples

### Business Intelligence
```sql
-- Detection trends over time
SELECT 
    DATE_TRUNC('day', created_at) as date,
    COUNT(*) as analyses,
    AVG(avg_confidence) as daily_confidence,
    array_agg(DISTINCT unnest(detected_classes)) as classes_detected
FROM analysis_results
GROUP BY DATE_TRUNC('day', created_at)
ORDER BY date DESC;
```

### Object Tracking Analysis
```sql
-- Track object size changes over time
SELECT 
    track_id,
    frame_index,
    ((bbox_norm_x2 - bbox_norm_x1) * (bbox_norm_y2 - bbox_norm_y1)) as size,
    confidence
FROM (
    SELECT 
        detection #>> '{track_id}' as track_id,
        (frame #>> '{frame_index}')::int as frame_index,
        (detection #> '{bbox_normalized}' ->> 'x1')::float as bbox_norm_x1,
        (detection #> '{bbox_normalized}' ->> 'y1')::float as bbox_norm_y1,
        (detection #> '{bbox_normalized}' ->> 'x2')::float as bbox_norm_x2,
        (detection #> '{bbox_normalized}' ->> 'y2')::float as bbox_norm_y2,
        (detection #>> '{confidence}')::float as confidence
    FROM analysis_results,
         jsonb_array_elements(results_data #> '{frames}') as frame,
         jsonb_array_elements(frame #> '{detections}') as detection
    WHERE id = 'your-analysis-id'
) t
WHERE track_id = '1'
ORDER BY frame_index;
```

## 🎯 Integration with Your Pipeline

### Modify Your YOLO Script
```python
# Add to your infer_yoloworld-vibecoder.py
from database.hybrid_video_db import HybridVideoAnalysisDB

def save_results_to_db(json_path, chunk_path, model_name, task_type, classes):
    with HybridVideoAnalysisDB(DB_CONNECTION) as db:
        chunk = db.get_chunk_by_path(chunk_path)
        if chunk:
            db.store_yolo_json_file(
                video_chunk_id=chunk['id'],
                json_file_path=json_path,
                model_name=model_name,
                task_type=task_type,
                prompt_classes=classes
            )
```

### Add Caption Storage
```python
def store_caption(chunk_path, caption, model_name):
    with HybridVideoAnalysisDB(DB_CONNECTION) as db:
        chunk = db.get_chunk_by_path(chunk_path)
        if chunk:
            db.store_dense_caption(
                video_chunk_id=chunk['id'],
                caption_text=caption,
                model_name=model_name
            )
```

## 🏆 Benefits Summary

| Feature | Normalized Only | JSONB Only | **Hybrid** |
|---------|----------------|------------|------------|
| **Query Speed** | ⚡ Fast | 🐌 Slow | ⚡ **Fast** |
| **Flexibility** | 🔒 Limited | 🔓 High | 🔓 **High** |
| **Storage Efficiency** | ✅ Good | ❌ Poor | ✅ **Good** |
| **Complex Analysis** | ❌ Hard | ✅ Easy | ✅ **Easy** |
| **Future-Proof** | ❌ No | ✅ Yes | ✅ **Yes** |

The hybrid approach gives you **maximum performance** for common queries while maintaining **complete flexibility** for complex analysis - perfect for your video analysis pipeline! 🎬
