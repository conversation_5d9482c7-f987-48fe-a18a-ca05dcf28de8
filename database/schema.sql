-- Video Analysis Database Schema
-- Designed for storing YOLO detection results and dense captions from video chunks

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- =============================================
-- CORE TABLES
-- =============================================

-- Videos table: stores original video metadata
CREATE TABLE videos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT,
    duration_seconds DECIMAL(10,3),
    fps DECIMAL(8,3),
    width INTEGER,
    height INTEGER,
    format VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    
    CONSTRAINT unique_video_path UNIQUE(file_path)
);

-- Video chunks table: stores information about video segments
CREATE TABLE video_chunks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    video_id UUID NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    start_time_seconds DECIMAL(10,3) NOT NULL,
    end_time_seconds DECIMAL(10,3) NOT NULL,
    duration_seconds DECIMAL(10,3) NOT NULL,
    width INTEGER,
    height INTEGER,
    total_frames INTEGER,
    fps DECIMAL(8,3),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_chunk_per_video UNIQUE(video_id, chunk_index),
    CONSTRAINT unique_chunk_path UNIQUE(file_path)
);

-- Analysis runs table: tracks different analysis sessions
CREATE TABLE analysis_runs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    video_chunk_id UUID NOT NULL REFERENCES video_chunks(id) ON DELETE CASCADE,
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50),
    task_type VARCHAR(50) NOT NULL, -- 'detect', 'track', 'segment'
    prompt_classes TEXT[], -- Array of detection classes
    parameters JSONB DEFAULT '{}',
    total_frames INTEGER,
    total_detections INTEGER,
    processing_time_seconds DECIMAL(10,3),
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- =============================================
-- DETECTION DATA TABLES
-- =============================================

-- Frames table: stores frame-level information
CREATE TABLE frames (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    analysis_run_id UUID NOT NULL REFERENCES analysis_runs(id) ON DELETE CASCADE,
    frame_index INTEGER NOT NULL,
    timestamp_seconds DECIMAL(10,3), -- Frame timestamp within chunk
    width INTEGER NOT NULL,
    height INTEGER NOT NULL,
    num_detections INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_frame_per_run UNIQUE(analysis_run_id, frame_index)
);

-- Object classes table: stores detection class information
CREATE TABLE object_classes (
    id SERIAL PRIMARY KEY,
    class_name VARCHAR(100) NOT NULL UNIQUE,
    class_id INTEGER,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Detections table: stores individual object detections
CREATE TABLE detections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    frame_id UUID NOT NULL REFERENCES frames(id) ON DELETE CASCADE,
    object_class_id INTEGER REFERENCES object_classes(id),
    class_name VARCHAR(100) NOT NULL,
    confidence DECIMAL(6,4) NOT NULL,
    
    -- Bounding box coordinates (absolute pixels)
    bbox_x1 DECIMAL(10,2) NOT NULL,
    bbox_y1 DECIMAL(10,2) NOT NULL,
    bbox_x2 DECIMAL(10,2) NOT NULL,
    bbox_y2 DECIMAL(10,2) NOT NULL,
    
    -- Normalized bounding box coordinates (0-1)
    bbox_norm_x1 DECIMAL(8,6) NOT NULL,
    bbox_norm_y1 DECIMAL(8,6) NOT NULL,
    bbox_norm_x2 DECIMAL(8,6) NOT NULL,
    bbox_norm_y2 DECIMAL(8,6) NOT NULL,
    
    -- Tracking information
    track_id INTEGER,
    
    -- Segmentation mask information
    has_mask BOOLEAN DEFAULT FALSE,
    mask_area INTEGER,
    mask_shape INTEGER[],
    
    -- Spatial geometry for advanced queries (PostGIS)
    bbox_geometry GEOMETRY(POLYGON, 4326),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- CAPTION DATA TABLES
-- =============================================

-- Dense captions table: stores AI-generated descriptions
CREATE TABLE dense_captions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    video_chunk_id UUID NOT NULL REFERENCES video_chunks(id) ON DELETE CASCADE,
    caption_text TEXT NOT NULL,
    caption_type VARCHAR(50) DEFAULT 'dense', -- 'dense', 'summary', 'detailed'
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50),
    confidence_score DECIMAL(5,4),
    language VARCHAR(10) DEFAULT 'en',
    processing_time_seconds DECIMAL(10,3),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Full-text search
    caption_vector tsvector GENERATED ALWAYS AS (to_tsvector('english', caption_text)) STORED
);

-- Frame-level captions (optional: for frame-specific descriptions)
CREATE TABLE frame_captions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    frame_id UUID NOT NULL REFERENCES frames(id) ON DELETE CASCADE,
    caption_text TEXT NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    confidence_score DECIMAL(5,4),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Video and chunk indexes
CREATE INDEX idx_videos_filename ON videos(filename);
CREATE INDEX idx_videos_created_at ON videos(created_at);
CREATE INDEX idx_video_chunks_video_id ON video_chunks(video_id);
CREATE INDEX idx_video_chunks_chunk_index ON video_chunks(chunk_index);
CREATE INDEX idx_video_chunks_time_range ON video_chunks(start_time_seconds, end_time_seconds);

-- Analysis and detection indexes
CREATE INDEX idx_analysis_runs_chunk_id ON analysis_runs(video_chunk_id);
CREATE INDEX idx_analysis_runs_model ON analysis_runs(model_name, task_type);
CREATE INDEX idx_analysis_runs_status ON analysis_runs(status);
CREATE INDEX idx_analysis_runs_created_at ON analysis_runs(created_at);

CREATE INDEX idx_frames_analysis_run ON frames(analysis_run_id);
CREATE INDEX idx_frames_index ON frames(frame_index);
CREATE INDEX idx_frames_timestamp ON frames(timestamp_seconds);

CREATE INDEX idx_detections_frame_id ON detections(frame_id);
CREATE INDEX idx_detections_class ON detections(class_name);
CREATE INDEX idx_detections_confidence ON detections(confidence);
CREATE INDEX idx_detections_track_id ON detections(track_id) WHERE track_id IS NOT NULL;
CREATE INDEX idx_detections_bbox ON detections(bbox_x1, bbox_y1, bbox_x2, bbox_y2);

-- Spatial index for bounding boxes (PostGIS)
CREATE INDEX idx_detections_bbox_geometry ON detections USING GIST(bbox_geometry);

-- Caption indexes
CREATE INDEX idx_dense_captions_chunk_id ON dense_captions(video_chunk_id);
CREATE INDEX idx_dense_captions_model ON dense_captions(model_name);
CREATE INDEX idx_dense_captions_created_at ON dense_captions(created_at);
CREATE INDEX idx_dense_captions_fts ON dense_captions USING GIN(caption_vector);

CREATE INDEX idx_frame_captions_frame_id ON frame_captions(frame_id);

-- =============================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =============================================

-- Update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_videos_updated_at BEFORE UPDATE ON videos
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Auto-populate bounding box geometry
CREATE OR REPLACE FUNCTION update_bbox_geometry()
RETURNS TRIGGER AS $$
BEGIN
    NEW.bbox_geometry = ST_MakeEnvelope(
        NEW.bbox_norm_x1, NEW.bbox_norm_y1,
        NEW.bbox_norm_x2, NEW.bbox_norm_y2,
        4326
    );
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_detections_geometry BEFORE INSERT OR UPDATE ON detections
    FOR EACH ROW EXECUTE FUNCTION update_bbox_geometry();
