# Video Analysis Database

A comprehensive PostgreSQL database schema and Python interface for storing video analysis results from YOLO object detection and dense captioning pipelines.

## Overview

This database system is designed to handle:
- **Video metadata** and chunk information
- **YOLO detection results** (bounding boxes, tracking, segmentation)
- **Dense captions** from multimodal AI models
- **Spatial queries** and full-text search
- **Performance analytics** and data quality monitoring

## Database Schema

### Core Tables

1. **`videos`** - Original video files metadata
2. **`video_chunks`** - Video segments (10-second chunks)
3. **`analysis_runs`** - YOLO/AI model execution records
4. **`frames`** - Individual frame information
5. **`detections`** - Object detection results with bounding boxes
6. **`dense_captions`** - AI-generated video descriptions
7. **`object_classes`** - Detection class definitions

### Key Features

- **UUID primary keys** for distributed systems
- **JSONB columns** for flexible metadata storage
- **PostGIS spatial indexing** for bounding box queries
- **Full-text search** on captions
- **Automatic triggers** for geometry updates
- **Comprehensive indexing** for performance

## Installation

### 1. Install PostgreSQL and PostGIS

```bash
# Ubuntu/Debian
sudo apt-get install postgresql postgresql-contrib postgis

# macOS
brew install postgresql postgis

# Start PostgreSQL service
sudo systemctl start postgresql  # Linux
brew services start postgresql   # macOS
```

### 2. Create Database

```sql
-- Connect as postgres user
sudo -u postgres psql

-- Create database and user
CREATE DATABASE video_analysis;
CREATE USER video_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE video_analysis TO video_user;

-- Connect to the new database
\c video_analysis

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
```

### 3. Install Python Dependencies

```bash
pip install -r database/requirements.txt
```

### 4. Set Up Schema

```bash
# Run the schema creation script
psql -d video_analysis -U video_user -f database/schema.sql
```

## Configuration

### Environment Variables

```bash
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=video_analysis
export DB_USER=video_user
export DB_PASSWORD=your_password
```

### Python Configuration

```python
from database.config import DatabaseConfig

# From environment variables
connection_string = DatabaseConfig.from_env()

# From dictionary
config = {
    "host": "localhost",
    "port": 5432,
    "database": "video_analysis",
    "user": "video_user",
    "password": "your_password"
}
connection_string = DatabaseConfig.from_dict(config)
```

## Usage Examples

### Basic Usage

```python
from database.video_analysis_db import VideoAnalysisDB, VideoInfo, VideoChunkInfo, AnalysisRunInfo
import json

# Connect to database
with VideoAnalysisDB(connection_string) as db:
    # Store video information
    video_info = VideoInfo(
        filename="cat-on-boat.mp4",
        file_path="/path/to/video.mp4",
        duration_seconds=30.0,
        fps=30.0,
        width=720,
        height=720
    )
    video_id = db.insert_video(video_info)
    
    # Store video chunk
    chunk_info = VideoChunkInfo(
        video_id=video_id,
        chunk_index=0,
        filename="chunk_000.mp4",
        file_path="/path/to/chunk_000.mp4",
        start_time_seconds=0.0,
        end_time_seconds=10.0,
        duration_seconds=10.0
    )
    chunk_id = db.insert_video_chunk(chunk_info)
    
    # Store YOLO results
    with open("output/chunk_000_track_results.json") as f:
        yolo_data = json.load(f)
    
    analysis_info = AnalysisRunInfo(
        video_chunk_id=chunk_id,
        model_name="yolov8x-worldv2",
        task_type="track",
        prompt_classes=["boat", "cat"]
    )
    analysis_run_id = db.create_analysis_run(analysis_info)
    db.store_yolo_results(analysis_run_id, yolo_data)
    
    # Store dense caption
    caption_id = db.store_dense_caption(
        video_chunk_id=chunk_id,
        caption_text="A cat sitting on a boat in calm water...",
        model_name="nvidia/vila"
    )
```

### Query Examples

```python
# Search captions
results = db.search_captions("cat boat water")

# Get detections by class
boat_detections = db.get_detections_by_class(
    analysis_run_id, "boat", min_confidence=0.7
)

# Track object movement
trajectory = db.get_track_trajectory(analysis_run_id, track_id=1)

# Get video summary
summary = db.get_video_summary(video_id)
```

## Integration with Existing Pipeline

### 1. Modify YOLO Inference Script

```python
# In your infer_yoloworld-vibecoder.py
from database.video_analysis_db import VideoAnalysisDB, AnalysisRunInfo

def save_to_database(results_json_path, chunk_path, model_name, task_type, prompt_classes):
    with VideoAnalysisDB(connection_string) as db:
        # Get or create video chunk record
        chunk = db.get_chunk_by_path(chunk_path)
        if not chunk:
            # Create chunk record first
            pass
        
        # Load and store results
        with open(results_json_path) as f:
            yolo_data = json.load(f)
        
        analysis_info = AnalysisRunInfo(
            video_chunk_id=chunk['id'],
            model_name=model_name,
            task_type=task_type,
            prompt_classes=prompt_classes,
            total_frames=yolo_data['total_frames'],
            total_detections=yolo_data['total_detections']
        )
        
        analysis_run_id = db.create_analysis_run(analysis_info)
        db.store_yolo_results(analysis_run_id, yolo_data)
```

### 2. Add Caption Storage

```python
# For your dense captioning pipeline
def store_caption(chunk_path, caption_text, model_name):
    with VideoAnalysisDB(connection_string) as db:
        chunk = db.get_chunk_by_path(chunk_path)
        if chunk:
            db.store_dense_caption(
                video_chunk_id=chunk['id'],
                caption_text=caption_text,
                model_name=model_name
            )
```

## Advanced Queries

The database supports complex analytical queries:

```sql
-- Find objects that appear together frequently
SELECT 
    d1.class_name as object1,
    d2.class_name as object2,
    COUNT(*) as co_occurrence_count
FROM detections d1
JOIN detections d2 ON d1.frame_id = d2.frame_id AND d1.id < d2.id
GROUP BY d1.class_name, d2.class_name
ORDER BY co_occurrence_count DESC;

-- Track object size changes over time
SELECT 
    track_id,
    frame_index,
    (bbox_x2 - bbox_x1) * (bbox_y2 - bbox_y1) as bbox_area,
    confidence
FROM detections d
JOIN frames f ON d.frame_id = f.id
WHERE track_id = 1
ORDER BY frame_index;

-- Find videos with specific caption content and detection patterns
SELECT DISTINCT v.filename
FROM videos v
JOIN video_chunks vc ON v.id = vc.video_id
JOIN dense_captions dc ON vc.id = dc.video_chunk_id
JOIN analysis_runs ar ON vc.id = ar.video_chunk_id
JOIN frames f ON ar.id = f.analysis_run_id
JOIN detections d ON f.id = d.frame_id
WHERE dc.caption_vector @@ plainto_tsquery('english', 'boat water')
  AND d.class_name = 'cat'
  AND d.confidence > 0.8;
```

## Performance Optimization

1. **Indexing**: All critical columns are indexed
2. **Partitioning**: Consider partitioning large tables by date
3. **Connection pooling**: Use pgbouncer for production
4. **Batch operations**: Use bulk inserts for large datasets

## Monitoring and Maintenance

```sql
-- Check database size
SELECT pg_size_pretty(pg_database_size('video_analysis'));

-- Monitor query performance
SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC;

-- Check index usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch 
FROM pg_stat_user_indexes ORDER BY idx_scan DESC;
```

## Backup and Recovery

```bash
# Backup
pg_dump -U video_user -h localhost video_analysis > backup.sql

# Restore
psql -U video_user -h localhost video_analysis < backup.sql
```
