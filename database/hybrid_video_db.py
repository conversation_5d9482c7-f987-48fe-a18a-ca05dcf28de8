"""
Hybrid Video Analysis Database Interface
Optimized for JSONB storage with extracted fields for performance
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any, Union
from uuid import UUID, uuid4
from dataclasses import dataclass
from pathlib import Path

import psycopg2
from psycopg2.extras import <PERSON>Dict<PERSON>urs<PERSON>, <PERSON><PERSON>
from psycopg2.extensions import register_adapter
import psycopg2.extras

# Register UUID adapter
psycopg2.extras.register_uuid()
register_adapter(dict, Json)

logger = logging.getLogger(__name__)


@dataclass
class VideoInfo:
    """Video metadata container"""
    filename: str
    file_path: str
    file_size: Optional[int] = None
    duration_seconds: Optional[float] = None
    fps: Optional[float] = None
    width: Optional[int] = None
    height: Optional[int] = None
    format: Optional[str] = None
    metadata: Optional[Dict] = None


@dataclass
class VideoChunkInfo:
    """Video chunk metadata container"""
    video_id: UUID
    chunk_index: int
    filename: str
    file_path: str
    start_time_seconds: float
    end_time_seconds: float
    duration_seconds: float
    width: Optional[int] = None
    height: Optional[int] = None
    fps: Optional[float] = None


@dataclass
class AnalysisResultInfo:
    """Analysis result metadata container"""
    video_chunk_id: UUID
    model_name: str
    task_type: str
    prompt_classes: List[str]
    results_data: Dict  # The full YOLO JSON
    model_version: Optional[str] = None
    processing_time_seconds: Optional[float] = None


class HybridVideoAnalysisDB:
    """Hybrid database interface optimized for JSONB + extracted fields"""
    
    def __init__(self, connection_string: str):
        """
        Initialize database connection
        
        Args:
            connection_string: PostgreSQL connection string
        """
        self.connection_string = connection_string
        self._connection = None
    
    def connect(self):
        """Establish database connection"""
        try:
            self._connection = psycopg2.connect(
                self.connection_string,
                cursor_factory=RealDictCursor
            )
            self._connection.autocommit = False
            logger.info("Connected to hybrid video analysis database")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise
    
    def disconnect(self):
        """Close database connection"""
        if self._connection:
            self._connection.close()
            self._connection = None
            logger.info("Disconnected from database")
    
    def __enter__(self):
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self._connection.rollback()
        else:
            self._connection.commit()
        self.disconnect()
    
    # =============================================
    # VIDEO MANAGEMENT (Same as before)
    # =============================================
    
    def insert_video(self, video_info: VideoInfo) -> UUID:
        """Insert a new video record"""
        with self._connection.cursor() as cursor:
            cursor.execute("""
                INSERT INTO videos (filename, file_path, file_size, duration_seconds, 
                                  fps, width, height, format, metadata)
                VALUES (%(filename)s, %(file_path)s, %(file_size)s, %(duration_seconds)s,
                       %(fps)s, %(width)s, %(height)s, %(format)s, %(metadata)s)
                ON CONFLICT (file_path) DO UPDATE SET
                    filename = EXCLUDED.filename,
                    file_size = EXCLUDED.file_size,
                    duration_seconds = EXCLUDED.duration_seconds,
                    fps = EXCLUDED.fps,
                    width = EXCLUDED.width,
                    height = EXCLUDED.height,
                    format = EXCLUDED.format,
                    metadata = EXCLUDED.metadata,
                    updated_at = NOW()
                RETURNING id
            """, {
                'filename': video_info.filename,
                'file_path': video_info.file_path,
                'file_size': video_info.file_size,
                'duration_seconds': video_info.duration_seconds,
                'fps': video_info.fps,
                'width': video_info.width,
                'height': video_info.height,
                'format': video_info.format,
                'metadata': video_info.metadata or {}
            })
            video_id = cursor.fetchone()['id']
            self._connection.commit()
            logger.info(f"Inserted video: {video_info.filename} with ID: {video_id}")
            return video_id
    
    def insert_video_chunk(self, chunk_info: VideoChunkInfo) -> UUID:
        """Insert a new video chunk record"""
        with self._connection.cursor() as cursor:
            cursor.execute("""
                INSERT INTO video_chunks (video_id, chunk_index, filename, file_path,
                                        start_time_seconds, end_time_seconds, duration_seconds,
                                        width, height, fps)
                VALUES (%(video_id)s, %(chunk_index)s, %(filename)s, %(file_path)s,
                       %(start_time_seconds)s, %(end_time_seconds)s, %(duration_seconds)s,
                       %(width)s, %(height)s, %(fps)s)
                ON CONFLICT (file_path) DO UPDATE SET
                    chunk_index = EXCLUDED.chunk_index,
                    start_time_seconds = EXCLUDED.start_time_seconds,
                    end_time_seconds = EXCLUDED.end_time_seconds,
                    duration_seconds = EXCLUDED.duration_seconds,
                    width = EXCLUDED.width,
                    height = EXCLUDED.height,
                    fps = EXCLUDED.fps
                RETURNING id
            """, {
                'video_id': chunk_info.video_id,
                'chunk_index': chunk_info.chunk_index,
                'filename': chunk_info.filename,
                'file_path': chunk_info.file_path,
                'start_time_seconds': chunk_info.start_time_seconds,
                'end_time_seconds': chunk_info.end_time_seconds,
                'duration_seconds': chunk_info.duration_seconds,
                'width': chunk_info.width,
                'height': chunk_info.height,
                'fps': chunk_info.fps
            })
            chunk_id = cursor.fetchone()['id']
            self._connection.commit()
            logger.info(f"Inserted video chunk: {chunk_info.filename} with ID: {chunk_id}")
            return chunk_id
    
    # =============================================
    # HYBRID ANALYSIS RESULTS
    # =============================================
    
    def store_analysis_result(self, analysis_info: AnalysisResultInfo) -> UUID:
        """
        Store complete analysis result with automatic field extraction
        
        The trigger will automatically extract summary fields from the JSONB data
        """
        with self._connection.cursor() as cursor:
            cursor.execute("""
                INSERT INTO analysis_results (
                    video_chunk_id, model_name, model_version, task_type,
                    prompt_classes, total_frames, total_detections,
                    processing_time_seconds, results_data
                ) VALUES (
                    %(video_chunk_id)s, %(model_name)s, %(model_version)s, %(task_type)s,
                    %(prompt_classes)s, %(total_frames)s, %(total_detections)s,
                    %(processing_time_seconds)s, %(results_data)s
                )
                RETURNING id, detected_classes, avg_confidence, max_confidence, min_confidence
            """, {
                'video_chunk_id': analysis_info.video_chunk_id,
                'model_name': analysis_info.model_name,
                'model_version': analysis_info.model_version,
                'task_type': analysis_info.task_type,
                'prompt_classes': analysis_info.prompt_classes,
                'total_frames': analysis_info.results_data.get('total_frames'),
                'total_detections': analysis_info.results_data.get('total_detections'),
                'processing_time_seconds': analysis_info.processing_time_seconds,
                'results_data': Json(analysis_info.results_data)
            })
            
            result = cursor.fetchone()
            analysis_id = result['id']
            
            self._connection.commit()
            logger.info(f"Stored analysis result with ID: {analysis_id}")
            logger.info(f"  Detected classes: {result['detected_classes']}")
            logger.info(f"  Avg confidence: {result['avg_confidence']}")
            
            return analysis_id
    
    def store_yolo_json_file(self, video_chunk_id: UUID, json_file_path: str, 
                           model_name: str, task_type: str, prompt_classes: List[str],
                           model_version: str = None, processing_time: float = None) -> UUID:
        """
        Convenience method to store YOLO results directly from JSON file
        """
        with open(json_file_path, 'r') as f:
            yolo_data = json.load(f)
        
        analysis_info = AnalysisResultInfo(
            video_chunk_id=video_chunk_id,
            model_name=model_name,
            task_type=task_type,
            prompt_classes=prompt_classes,
            results_data=yolo_data,
            model_version=model_version,
            processing_time_seconds=processing_time
        )
        
        return self.store_analysis_result(analysis_info)
    
    def store_dense_caption(self, video_chunk_id: UUID, caption_text: str, 
                          model_name: str, model_version: str = None,
                          confidence_score: float = None, 
                          processing_time_seconds: float = None,
                          caption_type: str = 'dense') -> UUID:
        """Store a dense caption for a video chunk"""
        with self._connection.cursor() as cursor:
            cursor.execute("""
                INSERT INTO dense_captions (video_chunk_id, caption_text, caption_type,
                                          model_name, model_version, confidence_score, 
                                          processing_time_seconds)
                VALUES (%(video_chunk_id)s, %(caption_text)s, %(caption_type)s,
                       %(model_name)s, %(model_version)s, %(confidence_score)s, 
                       %(processing_time_seconds)s)
                RETURNING id
            """, {
                'video_chunk_id': video_chunk_id,
                'caption_text': caption_text,
                'caption_type': caption_type,
                'model_name': model_name,
                'model_version': model_version,
                'confidence_score': confidence_score,
                'processing_time_seconds': processing_time_seconds
            })
            caption_id = cursor.fetchone()['id']
            self._connection.commit()
            logger.info(f"Stored dense caption with ID: {caption_id}")
            return caption_id

    # =============================================
    # HYBRID QUERY METHODS
    # =============================================

    def get_video_by_path(self, file_path: str) -> Optional[Dict]:
        """Get video record by file path"""
        with self._connection.cursor() as cursor:
            cursor.execute("SELECT * FROM videos WHERE file_path = %s", (file_path,))
            return cursor.fetchone()

    def get_chunk_by_path(self, file_path: str) -> Optional[Dict]:
        """Get video chunk by file path"""
        with self._connection.cursor() as cursor:
            cursor.execute("SELECT * FROM video_chunks WHERE file_path = %s", (file_path,))
            return cursor.fetchone()

    def get_analysis_results(self, video_chunk_id: UUID, model_name: str = None) -> List[Dict]:
        """Get analysis results for a video chunk"""
        with self._connection.cursor() as cursor:
            if model_name:
                cursor.execute("""
                    SELECT * FROM analysis_results
                    WHERE video_chunk_id = %s AND model_name = %s
                    ORDER BY created_at DESC
                """, (video_chunk_id, model_name))
            else:
                cursor.execute("""
                    SELECT * FROM analysis_results
                    WHERE video_chunk_id = %s
                    ORDER BY created_at DESC
                """, (video_chunk_id,))
            return cursor.fetchall()

    # =============================================
    # JSONB-POWERED QUERIES
    # =============================================

    def find_videos_with_classes(self, class_names: List[str],
                                min_confidence: float = 0.0,
                                require_all_classes: bool = False) -> List[Dict]:
        """
        Find videos containing specific object classes

        Args:
            class_names: List of class names to search for
            min_confidence: Minimum average confidence threshold
            require_all_classes: If True, video must contain ALL classes; if False, ANY class
        """
        with self._connection.cursor() as cursor:
            if require_all_classes:
                # Must contain ALL specified classes
                cursor.execute("""
                    SELECT DISTINCT
                        v.id, v.filename, v.file_path,
                        ar.model_name, ar.task_type,
                        ar.detected_classes, ar.avg_confidence,
                        ar.total_detections, ar.created_at
                    FROM videos v
                    JOIN video_chunks vc ON v.id = vc.video_id
                    JOIN analysis_results ar ON vc.id = ar.video_chunk_id
                    WHERE ar.detected_classes @> %s
                      AND ar.avg_confidence >= %s
                      AND ar.status = 'completed'
                    ORDER BY ar.avg_confidence DESC, ar.created_at DESC
                """, (class_names, min_confidence))
            else:
                # Must contain ANY of the specified classes
                cursor.execute("""
                    SELECT DISTINCT
                        v.id, v.filename, v.file_path,
                        ar.model_name, ar.task_type,
                        ar.detected_classes, ar.avg_confidence,
                        ar.total_detections, ar.created_at
                    FROM videos v
                    JOIN video_chunks vc ON v.id = vc.video_id
                    JOIN analysis_results ar ON vc.id = ar.video_chunk_id
                    WHERE ar.detected_classes && %s
                      AND ar.avg_confidence >= %s
                      AND ar.status = 'completed'
                    ORDER BY ar.avg_confidence DESC, ar.created_at DESC
                """, (class_names, min_confidence))
            return cursor.fetchall()

    def get_high_confidence_detections(self, analysis_result_id: UUID,
                                     min_confidence: float = 0.8,
                                     class_name: str = None) -> List[Dict]:
        """
        Extract high-confidence detections from JSONB data
        """
        with self._connection.cursor() as cursor:
            if class_name:
                cursor.execute("""
                    SELECT
                        (frame #>> '{frame_index}')::int as frame_index,
                        detection #>> '{class_name}' as class_name,
                        (detection #>> '{confidence}')::float as confidence,
                        detection #> '{bbox}' as bbox,
                        detection #> '{bbox_normalized}' as bbox_normalized,
                        detection #>> '{track_id}' as track_id
                    FROM analysis_results ar,
                         jsonb_array_elements(ar.results_data #> '{frames}') as frame,
                         jsonb_array_elements(frame #> '{detections}') as detection
                    WHERE ar.id = %s
                      AND (detection #>> '{confidence}')::float >= %s
                      AND detection #>> '{class_name}' = %s
                    ORDER BY (detection #>> '{confidence}')::float DESC,
                             (frame #>> '{frame_index}')::int
                """, (analysis_result_id, min_confidence, class_name))
            else:
                cursor.execute("""
                    SELECT
                        (frame #>> '{frame_index}')::int as frame_index,
                        detection #>> '{class_name}' as class_name,
                        (detection #>> '{confidence}')::float as confidence,
                        detection #> '{bbox}' as bbox,
                        detection #> '{bbox_normalized}' as bbox_normalized,
                        detection #>> '{track_id}' as track_id
                    FROM analysis_results ar,
                         jsonb_array_elements(ar.results_data #> '{frames}') as frame,
                         jsonb_array_elements(frame #> '{detections}') as detection
                    WHERE ar.id = %s
                      AND (detection #>> '{confidence}')::float >= %s
                    ORDER BY (detection #>> '{confidence}')::float DESC,
                             (frame #>> '{frame_index}')::int
                """, (analysis_result_id, min_confidence))
            return cursor.fetchall()

    def get_object_trajectory(self, analysis_result_id: UUID, track_id: int) -> List[Dict]:
        """
        Get trajectory of a tracked object across frames
        """
        with self._connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    (frame #>> '{frame_index}')::int as frame_index,
                    detection #>> '{class_name}' as class_name,
                    (detection #>> '{confidence}')::float as confidence,
                    detection #> '{bbox}' as bbox,
                    detection #> '{bbox_normalized}' as bbox_normalized,
                    -- Calculate center point
                    ((detection #> '{bbox_normalized}' ->> 'x1')::float +
                     (detection #> '{bbox_normalized}' ->> 'x2')::float) / 2 as center_x,
                    ((detection #> '{bbox_normalized}' ->> 'y1')::float +
                     (detection #> '{bbox_normalized}' ->> 'y2')::float) / 2 as center_y,
                    -- Calculate area
                    ((detection #> '{bbox_normalized}' ->> 'x2')::float -
                     (detection #> '{bbox_normalized}' ->> 'x1')::float) *
                    ((detection #> '{bbox_normalized}' ->> 'y2')::float -
                     (detection #> '{bbox_normalized}' ->> 'y1')::float) as normalized_area
                FROM analysis_results ar,
                     jsonb_array_elements(ar.results_data #> '{frames}') as frame,
                     jsonb_array_elements(frame #> '{detections}') as detection
                WHERE ar.id = %s
                  AND detection #>> '{track_id}' = %s
                ORDER BY (frame #>> '{frame_index}')::int
            """, (analysis_result_id, str(track_id)))
            return cursor.fetchall()

    def search_captions(self, search_text: str, limit: int = 10) -> List[Dict]:
        """Search dense captions using full-text search"""
        with self._connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    dc.*,
                    v.filename,
                    vc.chunk_index,
                    ts_rank(dc.caption_vector, plainto_tsquery('english', %s)) as rank
                FROM dense_captions dc
                JOIN video_chunks vc ON dc.video_chunk_id = vc.id
                JOIN videos v ON vc.video_id = v.id
                WHERE dc.caption_vector @@ plainto_tsquery('english', %s)
                ORDER BY rank DESC
                LIMIT %s
            """, (search_text, search_text, limit))
            return cursor.fetchall()

    def get_detection_timeline(self, analysis_result_id: UUID, class_name: str = None) -> List[Dict]:
        """
        Get detection timeline showing when objects appear/disappear
        """
        with self._connection.cursor() as cursor:
            if class_name:
                cursor.execute("""
                    SELECT
                        (frame #>> '{frame_index}')::int as frame_index,
                        COUNT(*) as detection_count,
                        AVG((detection #>> '{confidence}')::float) as avg_confidence,
                        array_agg(DISTINCT detection #>> '{track_id}')
                            FILTER (WHERE detection #>> '{track_id}' IS NOT NULL) as track_ids
                    FROM analysis_results ar,
                         jsonb_array_elements(ar.results_data #> '{frames}') as frame,
                         jsonb_array_elements(frame #> '{detections}') as detection
                    WHERE ar.id = %s
                      AND detection #>> '{class_name}' = %s
                    GROUP BY (frame #>> '{frame_index}')::int
                    ORDER BY (frame #>> '{frame_index}')::int
                """, (analysis_result_id, class_name))
            else:
                cursor.execute("""
                    SELECT
                        (frame #>> '{frame_index}')::int as frame_index,
                        COUNT(*) as detection_count,
                        AVG((detection #>> '{confidence}')::float) as avg_confidence,
                        array_agg(DISTINCT detection #>> '{class_name}') as class_names,
                        array_agg(DISTINCT detection #>> '{track_id}')
                            FILTER (WHERE detection #>> '{track_id}' IS NOT NULL) as track_ids
                    FROM analysis_results ar,
                         jsonb_array_elements(ar.results_data #> '{frames}') as frame,
                         jsonb_array_elements(frame #> '{detections}') as detection
                    WHERE ar.id = %s
                    GROUP BY (frame #>> '{frame_index}')::int
                    ORDER BY (frame #>> '{frame_index}')::int
                """, (analysis_result_id,))
            return cursor.fetchall()

    def get_class_statistics(self, video_id: UUID = None, model_name: str = None) -> List[Dict]:
        """
        Get detection statistics by class across videos or specific video
        """
        with self._connection.cursor() as cursor:
            base_query = """
                SELECT
                    class_name,
                    COUNT(*) as total_occurrences,
                    AVG(class_avg_confidence) as overall_avg_confidence,
                    MIN(class_avg_confidence) as min_confidence,
                    MAX(class_avg_confidence) as max_confidence,
                    SUM(class_detection_count) as total_detections,
                    COUNT(DISTINCT analysis_result_id) as videos_with_class
                FROM detection_stats ds
                WHERE 1=1
            """
            params = []

            if video_id:
                base_query += " AND ds.analysis_result_id IN (SELECT ar.id FROM analysis_results ar JOIN video_chunks vc ON ar.video_chunk_id = vc.id WHERE vc.video_id = %s)"
                params.append(video_id)

            if model_name:
                base_query += " AND ds.model_name = %s"
                params.append(model_name)

            base_query += """
                GROUP BY class_name
                ORDER BY total_detections DESC, overall_avg_confidence DESC
            """

            cursor.execute(base_query, params)
            return cursor.fetchall()

    def find_co_occurring_objects(self, analysis_result_id: UUID,
                                min_co_occurrence: int = 5) -> List[Dict]:
        """
        Find objects that frequently appear together in the same frames
        """
        with self._connection.cursor() as cursor:
            cursor.execute("""
                WITH frame_detections AS (
                    SELECT
                        (frame #>> '{frame_index}')::int as frame_index,
                        array_agg(DISTINCT detection #>> '{class_name}') as classes_in_frame
                    FROM analysis_results ar,
                         jsonb_array_elements(ar.results_data #> '{frames}') as frame,
                         jsonb_array_elements(frame #> '{detections}') as detection
                    WHERE ar.id = %s
                    GROUP BY (frame #>> '{frame_index}')::int
                ),
                class_pairs AS (
                    SELECT
                        c1.class_name as class1,
                        c2.class_name as class2,
                        COUNT(*) as co_occurrence_count
                    FROM frame_detections fd,
                         unnest(fd.classes_in_frame) as c1(class_name),
                         unnest(fd.classes_in_frame) as c2(class_name)
                    WHERE c1.class_name < c2.class_name  -- Avoid duplicates and self-pairs
                    GROUP BY c1.class_name, c2.class_name
                )
                SELECT * FROM class_pairs
                WHERE co_occurrence_count >= %s
                ORDER BY co_occurrence_count DESC
            """, (analysis_result_id, min_co_occurrence))
            return cursor.fetchall()

    def get_video_summary(self, video_id: UUID) -> Dict:
        """Get comprehensive summary of video analysis"""
        with self._connection.cursor() as cursor:
            # Get video info
            cursor.execute("SELECT * FROM videos WHERE id = %s", (video_id,))
            video = cursor.fetchone()

            if not video:
                return None

            # Get chunk count and analysis summary
            cursor.execute("""
                SELECT
                    COUNT(DISTINCT vc.id) as chunk_count,
                    COUNT(DISTINCT ar.id) as analysis_count,
                    array_agg(DISTINCT ar.model_name) as models_used,
                    array_agg(DISTINCT ar.task_type) as task_types,
                    SUM(ar.total_detections) as total_detections,
                    AVG(ar.avg_confidence) as overall_avg_confidence,
                    array_agg(DISTINCT unnest(ar.detected_classes)) as all_detected_classes
                FROM video_chunks vc
                LEFT JOIN analysis_results ar ON vc.id = ar.video_chunk_id
                WHERE vc.video_id = %s AND ar.status = 'completed'
            """, (video_id,))
            analysis_summary = cursor.fetchone()

            # Get caption count
            cursor.execute("""
                SELECT COUNT(*) as caption_count
                FROM dense_captions dc
                JOIN video_chunks vc ON dc.video_chunk_id = vc.id
                WHERE vc.video_id = %s
            """, (video_id,))
            caption_count = cursor.fetchone()['caption_count']

            return {
                'video': dict(video),
                'chunk_count': analysis_summary['chunk_count'],
                'analysis_count': analysis_summary['analysis_count'],
                'models_used': analysis_summary['models_used'],
                'task_types': analysis_summary['task_types'],
                'total_detections': analysis_summary['total_detections'] or 0,
                'overall_avg_confidence': float(analysis_summary['overall_avg_confidence']) if analysis_summary['overall_avg_confidence'] else 0,
                'all_detected_classes': analysis_summary['all_detected_classes'] or [],
                'caption_count': caption_count
            }

    def refresh_materialized_views(self):
        """Refresh materialized views for updated statistics"""
        with self._connection.cursor() as cursor:
            cursor.execute("SELECT refresh_detection_stats()")
            self._connection.commit()
            logger.info("Refreshed materialized views")

    # =============================================
    # ADVANCED ANALYTICS
    # =============================================

    def get_model_performance_comparison(self) -> List[Dict]:
        """Compare performance metrics across different models"""
        with self._connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    model_name,
                    task_type,
                    COUNT(*) as total_runs,
                    AVG(processing_time_seconds) as avg_processing_time,
                    AVG(total_detections::float / total_frames) as avg_detections_per_frame,
                    AVG(avg_confidence) as avg_confidence_score,
                    COUNT(*) FILTER (WHERE status = 'completed') as successful_runs,
                    COUNT(*) FILTER (WHERE status = 'failed') as failed_runs
                FROM analysis_results
                GROUP BY model_name, task_type
                ORDER BY avg_confidence_score DESC, avg_processing_time ASC
            """)
            return cursor.fetchall()

    def find_similar_videos(self, video_id: UUID, similarity_threshold: float = 0.7) -> List[Dict]:
        """
        Find videos with similar object detection patterns
        Uses Jaccard similarity on detected classes
        """
        with self._connection.cursor() as cursor:
            cursor.execute("""
                WITH target_video AS (
                    SELECT array_agg(DISTINCT unnest(ar.detected_classes)) as target_classes
                    FROM analysis_results ar
                    JOIN video_chunks vc ON ar.video_chunk_id = vc.id
                    WHERE vc.video_id = %s AND ar.status = 'completed'
                ),
                video_similarities AS (
                    SELECT
                        v.id,
                        v.filename,
                        array_agg(DISTINCT unnest(ar.detected_classes)) as video_classes,
                        -- Jaccard similarity: intersection / union
                        (SELECT COUNT(*) FROM unnest(array_agg(DISTINCT unnest(ar.detected_classes)))
                         INTERSECT
                         SELECT unnest(target_classes) FROM target_video)::float /
                        (SELECT COUNT(*) FROM (
                            SELECT unnest(array_agg(DISTINCT unnest(ar.detected_classes)))
                            UNION
                            SELECT unnest(target_classes) FROM target_video
                        ) as union_classes)::float as similarity_score
                    FROM videos v
                    JOIN video_chunks vc ON v.id = vc.video_id
                    JOIN analysis_results ar ON vc.id = ar.video_chunk_id
                    WHERE v.id != %s AND ar.status = 'completed'
                    GROUP BY v.id, v.filename
                )
                SELECT * FROM video_similarities
                WHERE similarity_score >= %s
                ORDER BY similarity_score DESC
            """, (video_id, video_id, similarity_threshold))
            return cursor.fetchall()
