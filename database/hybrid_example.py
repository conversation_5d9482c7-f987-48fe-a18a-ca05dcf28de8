#!/usr/bin/env python3
"""
Hybrid Video Analysis Database Example
Demonstrates the power of combining normalized fields with JSONB storage
"""

import json
import os
import sys
from pathlib import Path
from datetime import datetime

# Add the parent directory to the path to import our modules
sys.path.append(str(Path(__file__).parent.parent))

from database.hybrid_video_db import (
    HybridVideoAnalysisDB, VideoInfo, VideoChunkInfo, AnalysisResultInfo
)


def main():
    """Example workflow using the hybrid database approach"""
    
    # Database connection string - adjust for your setup
    DB_CONNECTION = "postgresql://username:password@localhost:5432/video_analysis"
    
    # Example paths
    ORIGINAL_VIDEO_PATH = "cat-on-boat.mp4"
    YOLO_RESULTS_PATH = "output/chunk_000_track_results.json"
    
    try:
        with HybridVideoAnalysisDB(DB_CONNECTION) as db:
            print("🔗 Connected to hybrid database successfully!")
            
            # =============================================
            # 1. Store video and chunk information
            # =============================================
            print("\n📹 1. Storing video information...")
            
            video_info = VideoInfo(
                filename="cat-on-boat.mp4",
                file_path=ORIGINAL_VIDEO_PATH,
                duration_seconds=30.0,
                fps=30.0,
                width=720,
                height=720,
                format="mp4"
            )
            
            video_id = db.insert_video(video_info)
            print(f"   ✅ Stored video with ID: {video_id}")
            
            chunk_info = VideoChunkInfo(
                video_id=video_id,
                chunk_index=0,
                filename="chunk_000.mp4",
                file_path="../video_chunks/chunk_000.mp4",
                start_time_seconds=0.0,
                end_time_seconds=10.0,
                duration_seconds=10.0,
                width=720,
                height=720,
                fps=30.0
            )
            
            chunk_id = db.insert_video_chunk(chunk_info)
            print(f"   ✅ Stored video chunk with ID: {chunk_id}")
            
            # =============================================
            # 2. Store YOLO results using hybrid approach
            # =============================================
            print("\n🤖 2. Storing YOLO analysis results...")
            
            if os.path.exists(YOLO_RESULTS_PATH):
                # Store directly from JSON file
                analysis_id = db.store_yolo_json_file(
                    video_chunk_id=chunk_id,
                    json_file_path=YOLO_RESULTS_PATH,
                    model_name="yolov8x-worldv2",
                    task_type="track",
                    prompt_classes=["boat", "cat"],
                    model_version="v2",
                    processing_time=15.2
                )
                print(f"   ✅ Stored analysis result with ID: {analysis_id}")
                
                # Get the stored result to see extracted fields
                results = db.get_analysis_results(chunk_id)
                if results:
                    result = results[0]
                    print(f"   📊 Extracted fields:")
                    print(f"      - Detected classes: {result['detected_classes']}")
                    print(f"      - Avg confidence: {result['avg_confidence']}")
                    print(f"      - Total detections: {result['total_detections']}")
                    print(f"      - Unique tracks: {result['unique_track_count']}")
                
            else:
                print(f"   ⚠️  YOLO results file not found: {YOLO_RESULTS_PATH}")
                # Create dummy data for demonstration
                dummy_yolo_data = {
                    "source": "../video_chunks/chunk_000.mp4",
                    "total_frames": 10,
                    "total_detections": 25,
                    "frames": [
                        {
                            "frame_index": 0,
                            "image_shape": {"height": 720, "width": 720},
                            "detections": [
                                {
                                    "class_id": 1,
                                    "class_name": "boat",
                                    "confidence": 0.85,
                                    "bbox": {"x1": 100, "y1": 200, "x2": 300, "y2": 400},
                                    "bbox_normalized": {"x1": 0.139, "y1": 0.278, "x2": 0.417, "y2": 0.556},
                                    "track_id": 1
                                },
                                {
                                    "class_id": 0,
                                    "class_name": "cat",
                                    "confidence": 0.92,
                                    "bbox": {"x1": 150, "y1": 100, "x2": 250, "y2": 200},
                                    "bbox_normalized": {"x1": 0.208, "y1": 0.139, "x2": 0.347, "y2": 0.278},
                                    "track_id": 2
                                }
                            ],
                            "num_detections": 2
                        }
                    ]
                }
                
                analysis_info = AnalysisResultInfo(
                    video_chunk_id=chunk_id,
                    model_name="yolov8x-worldv2",
                    task_type="track",
                    prompt_classes=["boat", "cat"],
                    results_data=dummy_yolo_data,
                    processing_time_seconds=5.0
                )
                
                analysis_id = db.store_analysis_result(analysis_info)
                print(f"   ✅ Stored dummy analysis result with ID: {analysis_id}")
            
            # =============================================
            # 3. Store dense caption
            # =============================================
            print("\n💬 3. Storing dense caption...")
            
            sample_caption = (
                "A cat is sitting on a white boat floating on calm blue water. "
                "The boat appears to be a small recreational vessel. There are other "
                "boats visible in the background. The scene is captured during daytime "
                "with clear visibility and good lighting conditions."
            )
            
            caption_id = db.store_dense_caption(
                video_chunk_id=chunk_id,
                caption_text=sample_caption,
                model_name="nvidia/vila",
                model_version="1.0",
                confidence_score=0.88,
                processing_time_seconds=2.1
            )
            print(f"   ✅ Stored dense caption with ID: {caption_id}")
            
            # =============================================
            # 4. Demonstrate hybrid query capabilities
            # =============================================
            print("\n🔍 4. Demonstrating hybrid query capabilities...")
            
            # Fast queries using extracted fields
            print("\n   📈 Fast queries using extracted fields:")
            videos_with_cats = db.find_videos_with_classes(["cat"], min_confidence=0.8)
            print(f"      - Videos with cats (>80% confidence): {len(videos_with_cats)}")
            
            videos_with_both = db.find_videos_with_classes(["cat", "boat"], require_all_classes=True)
            print(f"      - Videos with both cats AND boats: {len(videos_with_both)}")
            
            # JSONB queries for detailed analysis
            print("\n   🔬 JSONB queries for detailed analysis:")
            if 'analysis_id' in locals():
                high_conf_detections = db.get_high_confidence_detections(
                    analysis_id, min_confidence=0.8
                )
                print(f"      - High confidence detections: {len(high_conf_detections)}")
                
                if high_conf_detections:
                    detection = high_conf_detections[0]
                    print(f"        Best detection: {detection['class_name']} "
                          f"(confidence: {detection['confidence']:.3f}, "
                          f"frame: {detection['frame_index']})")
                
                # Object trajectory analysis
                trajectory = db.get_object_trajectory(analysis_id, track_id=1)
                print(f"      - Track 1 trajectory points: {len(trajectory)}")
                
                if len(trajectory) > 1:
                    start = trajectory[0]
                    end = trajectory[-1]
                    print(f"        Movement: ({start['center_x']:.3f}, {start['center_y']:.3f}) → "
                          f"({end['center_x']:.3f}, {end['center_y']:.3f})")
            
            # Caption search
            print("\n   🔎 Caption search:")
            caption_results = db.search_captions("boat water cat")
            print(f"      - Caption search results: {len(caption_results)}")
            if caption_results:
                result = caption_results[0]
                print(f"        Best match: {result['filename']} "
                      f"(rank: {result['rank']:.3f})")
                print(f"        Caption: {result['caption_text'][:100]}...")
            
            # Video summary
            print("\n   📋 Video summary:")
            summary = db.get_video_summary(video_id)
            if summary:
                print(f"      - Filename: {summary['video']['filename']}")
                print(f"      - Chunks analyzed: {summary['chunk_count']}")
                print(f"      - Total detections: {summary['total_detections']}")
                print(f"      - Overall confidence: {summary['overall_avg_confidence']:.3f}")
                print(f"      - Detected classes: {summary['all_detected_classes']}")
                print(f"      - Models used: {summary['models_used']}")
                print(f"      - Captions: {summary['caption_count']}")
            
            # =============================================
            # 5. Advanced analytics
            # =============================================
            print("\n📊 5. Advanced analytics...")
            
            # Class statistics
            class_stats = db.get_class_statistics()
            print(f"\n   🏷️  Class statistics ({len(class_stats)} classes):")
            for stat in class_stats[:3]:  # Show top 3
                print(f"      - {stat['class_name']}: {stat['total_detections']} detections, "
                      f"avg confidence: {stat['overall_avg_confidence']:.3f}")
            
            # Model performance
            model_performance = db.get_model_performance_comparison()
            print(f"\n   ⚡ Model performance ({len(model_performance)} models):")
            for perf in model_performance:
                print(f"      - {perf['model_name']} ({perf['task_type']}): "
                      f"{perf['avg_processing_time']:.1f}s avg, "
                      f"{perf['avg_confidence_score']:.3f} confidence")
            
            # Co-occurring objects
            if 'analysis_id' in locals():
                co_occurrences = db.find_co_occurring_objects(analysis_id, min_co_occurrence=1)
                print(f"\n   🤝 Co-occurring objects: {len(co_occurrences)} pairs")
                for co_occ in co_occurrences:
                    print(f"      - {co_occ['class1']} + {co_occ['class2']}: "
                          f"{co_occ['co_occurrence_count']} times together")
            
            # Refresh materialized views for updated stats
            print("\n   🔄 Refreshing materialized views...")
            db.refresh_materialized_views()
            
            print("\n✅ Hybrid database example completed successfully!")
            print("\n💡 Key advantages of the hybrid approach:")
            print("   - Fast queries using extracted fields (detected_classes, avg_confidence)")
            print("   - Flexible JSONB queries for detailed analysis")
            print("   - Automatic field extraction via database triggers")
            print("   - Best of both worlds: performance + flexibility")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
