-- Sample Queries for Video Analysis Database
-- Demonstrates common use cases and query patterns

-- =============================================
-- BASIC QUERIES
-- =============================================

-- 1. Get all videos with their chunk counts
SELECT 
    v.id,
    v.filename,
    v.duration_seconds,
    COUNT(vc.id) as chunk_count,
    v.created_at
FROM videos v
LEFT JOIN video_chunks vc ON v.id = vc.video_id
GROUP BY v.id, v.filename, v.duration_seconds, v.created_at
ORDER BY v.created_at DESC;

-- 2. Get analysis summary for a specific video
SELECT 
    v.filename,
    ar.model_name,
    ar.task_type,
    ar.total_frames,
    ar.total_detections,
    ar.processing_time_seconds,
    ar.status,
    ar.created_at
FROM videos v
JOIN video_chunks vc ON v.id = vc.video_id
JOIN analysis_runs ar ON vc.id = ar.video_chunk_id
WHERE v.filename = 'cat-on-boat.mp4'
ORDER BY ar.created_at DESC;

-- =============================================
-- DETECTION ANALYSIS QUERIES
-- =============================================

-- 3. Find all detections of a specific class with high confidence
SELECT 
    v.filename,
    vc.chunk_index,
    f.frame_index,
    d.class_name,
    d.confidence,
    d.bbox_x1, d.bbox_y1, d.bbox_x2, d.bbox_y2,
    d.track_id
FROM detections d
JOIN frames f ON d.frame_id = f.id
JOIN analysis_runs ar ON f.analysis_run_id = ar.id
JOIN video_chunks vc ON ar.video_chunk_id = vc.id
JOIN videos v ON vc.video_id = v.id
WHERE d.class_name = 'boat' 
  AND d.confidence > 0.7
ORDER BY v.filename, vc.chunk_index, f.frame_index;

-- 4. Track object movement across frames (for tracking tasks)
SELECT 
    v.filename,
    vc.chunk_index,
    d.track_id,
    d.class_name,
    f.frame_index,
    f.timestamp_seconds,
    d.confidence,
    -- Calculate bounding box center
    (d.bbox_x1 + d.bbox_x2) / 2 as center_x,
    (d.bbox_y1 + d.bbox_y2) / 2 as center_y,
    -- Calculate bounding box area
    (d.bbox_x2 - d.bbox_x1) * (d.bbox_y2 - d.bbox_y1) as bbox_area
FROM detections d
JOIN frames f ON d.frame_id = f.id
JOIN analysis_runs ar ON f.analysis_run_id = ar.id
JOIN video_chunks vc ON ar.video_chunk_id = vc.id
JOIN videos v ON vc.video_id = v.id
WHERE d.track_id IS NOT NULL
  AND v.filename = 'cat-on-boat.mp4'
  AND d.track_id = 1
ORDER BY vc.chunk_index, f.frame_index;

-- 5. Detection statistics by class
SELECT 
    d.class_name,
    COUNT(*) as total_detections,
    AVG(d.confidence) as avg_confidence,
    MIN(d.confidence) as min_confidence,
    MAX(d.confidence) as max_confidence,
    COUNT(DISTINCT d.track_id) as unique_tracks
FROM detections d
JOIN frames f ON d.frame_id = f.id
JOIN analysis_runs ar ON f.analysis_run_id = ar.id
WHERE ar.task_type = 'track'
GROUP BY d.class_name
ORDER BY total_detections DESC;

-- =============================================
-- TEMPORAL ANALYSIS QUERIES
-- =============================================

-- 6. Detection frequency over time (frames)
SELECT 
    v.filename,
    vc.chunk_index,
    f.frame_index,
    f.timestamp_seconds,
    COUNT(d.id) as detection_count,
    STRING_AGG(DISTINCT d.class_name, ', ') as detected_classes
FROM frames f
JOIN analysis_runs ar ON f.analysis_run_id = ar.id
JOIN video_chunks vc ON ar.video_chunk_id = vc.id
JOIN videos v ON vc.video_id = v.id
LEFT JOIN detections d ON f.id = d.frame_id
GROUP BY v.filename, vc.chunk_index, f.frame_index, f.timestamp_seconds
ORDER BY v.filename, vc.chunk_index, f.frame_index;

-- 7. Find frames with multiple objects of the same class
SELECT 
    v.filename,
    vc.chunk_index,
    f.frame_index,
    d.class_name,
    COUNT(*) as object_count,
    AVG(d.confidence) as avg_confidence
FROM detections d
JOIN frames f ON d.frame_id = f.id
JOIN analysis_runs ar ON f.analysis_run_id = ar.id
JOIN video_chunks vc ON ar.video_chunk_id = vc.id
JOIN videos v ON vc.video_id = v.id
GROUP BY v.filename, vc.chunk_index, f.frame_index, d.class_name
HAVING COUNT(*) > 1
ORDER BY object_count DESC, avg_confidence DESC;

-- =============================================
-- CAPTION QUERIES
-- =============================================

-- 8. Search captions by text content
SELECT 
    v.filename,
    vc.chunk_index,
    dc.caption_text,
    dc.model_name,
    dc.confidence_score,
    dc.created_at
FROM dense_captions dc
JOIN video_chunks vc ON dc.video_chunk_id = vc.id
JOIN videos v ON vc.video_id = v.id
WHERE dc.caption_vector @@ plainto_tsquery('english', 'boat water ocean')
ORDER BY ts_rank(dc.caption_vector, plainto_tsquery('english', 'boat water ocean')) DESC;

-- 9. Get captions with corresponding detection summary
SELECT 
    v.filename,
    vc.chunk_index,
    dc.caption_text,
    dc.model_name as caption_model,
    ar.model_name as detection_model,
    ar.total_detections,
    STRING_AGG(DISTINCT d.class_name, ', ') as detected_objects
FROM dense_captions dc
JOIN video_chunks vc ON dc.video_chunk_id = vc.id
JOIN videos v ON vc.video_id = v.id
JOIN analysis_runs ar ON vc.id = ar.video_chunk_id
JOIN frames f ON ar.id = f.analysis_run_id
JOIN detections d ON f.id = d.frame_id
GROUP BY v.filename, vc.chunk_index, dc.caption_text, dc.model_name, ar.model_name, ar.total_detections
ORDER BY v.filename, vc.chunk_index;

-- =============================================
-- SPATIAL QUERIES (PostGIS)
-- =============================================

-- 10. Find overlapping bounding boxes (objects that might be interacting)
SELECT DISTINCT
    v.filename,
    vc.chunk_index,
    f.frame_index,
    d1.class_name as object1_class,
    d1.track_id as object1_track,
    d2.class_name as object2_class,
    d2.track_id as object2_track,
    ST_Area(ST_Intersection(d1.bbox_geometry, d2.bbox_geometry)) as overlap_area
FROM detections d1
JOIN detections d2 ON d1.frame_id = d2.frame_id AND d1.id < d2.id
JOIN frames f ON d1.frame_id = f.id
JOIN analysis_runs ar ON f.analysis_run_id = ar.id
JOIN video_chunks vc ON ar.video_chunk_id = vc.id
JOIN videos v ON vc.video_id = v.id
WHERE ST_Intersects(d1.bbox_geometry, d2.bbox_geometry)
  AND ST_Area(ST_Intersection(d1.bbox_geometry, d2.bbox_geometry)) > 0.01
ORDER BY overlap_area DESC;

-- =============================================
-- PERFORMANCE AND MONITORING QUERIES
-- =============================================

-- 11. Analysis performance metrics
SELECT 
    ar.model_name,
    ar.task_type,
    COUNT(*) as total_runs,
    AVG(ar.processing_time_seconds) as avg_processing_time,
    AVG(ar.total_detections::float / ar.total_frames) as avg_detections_per_frame,
    COUNT(*) FILTER (WHERE ar.status = 'completed') as successful_runs,
    COUNT(*) FILTER (WHERE ar.status = 'failed') as failed_runs
FROM analysis_runs ar
GROUP BY ar.model_name, ar.task_type
ORDER BY avg_processing_time DESC;

-- 12. Database storage statistics
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation,
    most_common_vals
FROM pg_stats 
WHERE schemaname = 'public' 
  AND tablename IN ('videos', 'video_chunks', 'analysis_runs', 'frames', 'detections', 'dense_captions')
ORDER BY tablename, attname;

-- =============================================
-- DATA QUALITY QUERIES
-- =============================================

-- 13. Find potential data quality issues
-- Missing captions for processed chunks
SELECT 
    v.filename,
    vc.chunk_index,
    ar.model_name,
    ar.status,
    ar.completed_at
FROM video_chunks vc
JOIN videos v ON vc.video_id = v.id
JOIN analysis_runs ar ON vc.id = ar.video_chunk_id
LEFT JOIN dense_captions dc ON vc.id = dc.video_chunk_id
WHERE ar.status = 'completed' 
  AND dc.id IS NULL
ORDER BY ar.completed_at DESC;

-- 14. Detect anomalous detection counts
WITH detection_stats AS (
    SELECT 
        ar.id as analysis_run_id,
        ar.model_name,
        COUNT(d.id) as actual_detections,
        ar.total_detections as reported_detections
    FROM analysis_runs ar
    JOIN frames f ON ar.id = f.analysis_run_id
    LEFT JOIN detections d ON f.id = d.frame_id
    GROUP BY ar.id, ar.model_name, ar.total_detections
)
SELECT 
    analysis_run_id,
    model_name,
    actual_detections,
    reported_detections,
    ABS(actual_detections - reported_detections) as discrepancy
FROM detection_stats
WHERE actual_detections != reported_detections
ORDER BY discrepancy DESC;
