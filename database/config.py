"""
Configuration settings for Video Analysis Database
"""

import os
from typing import Dict, Any


class DatabaseConfig:
    """Database configuration settings"""
    
    # Default connection parameters
    DEFAULT_HOST = "localhost"
    DEFAULT_PORT = 5432
    DEFAULT_DATABASE = "video_analysis"
    DEFAULT_USER = "postgres"
    
    @classmethod
    def from_env(cls) -> str:
        """
        Create connection string from environment variables
        
        Environment variables:
        - DB_HOST: Database host (default: localhost)
        - DB_PORT: Database port (default: 5432)
        - DB_NAME: Database name (default: video_analysis)
        - DB_USER: Database user (default: postgres)
        - DB_PASSWORD: Database password (required)
        """
        host = os.getenv("DB_HOST", cls.DEFAULT_HOST)
        port = os.getenv("DB_PORT", cls.DEFAULT_PORT)
        database = os.getenv("DB_NAME", cls.DEFAULT_DATABASE)
        user = os.getenv("DB_USER", cls.DEFAULT_USER)
        password = os.getenv("DB_PASSWORD")
        
        if not password:
            raise ValueError("DB_PASSWORD environment variable is required")
        
        return f"postgresql://{user}:{password}@{host}:{port}/{database}"
    
    @classmethod
    def from_dict(cls, config: Dict[str, Any]) -> str:
        """Create connection string from configuration dictionary"""
        required_keys = ["host", "port", "database", "user", "password"]
        for key in required_keys:
            if key not in config:
                raise ValueError(f"Missing required configuration key: {key}")
        
        return (f"postgresql://{config['user']}:{config['password']}"
                f"@{config['host']}:{config['port']}/{config['database']}")


class ModelConfig:
    """Configuration for different AI models"""
    
    YOLO_MODELS = {
        "yoloworldx": {
            "weights": "data/weights/yolov8x-worldv2.pt",
            "type": "detection",
            "supports_tracking": True,
            "supports_segmentation": False
        },
        "yoloworldm": {
            "weights": "data/weights/yolov8m-worldv2.pt",
            "type": "detection",
            "supports_tracking": True,
            "supports_segmentation": False
        },
        "yoloworldl": {
            "weights": "data/weights/yolov8l-worldv2.pt",
            "type": "detection",
            "supports_tracking": True,
            "supports_segmentation": False
        },
        "yoloes": {
            "weights": "data/weights/yoloe-11s-seg.pt",
            "type": "segmentation",
            "supports_tracking": True,
            "supports_segmentation": True
        },
        "yoloem": {
            "weights": "data/weights/yoloe-11m-seg.pt",
            "type": "segmentation",
            "supports_tracking": True,
            "supports_segmentation": True
        },
        "yoloel": {
            "weights": "data/weights/yoloe-11l-seg.pt",
            "type": "segmentation",
            "supports_tracking": True,
            "supports_segmentation": True
        }
    }
    
    CAPTION_MODELS = {
        "nvidia/vila": {
            "type": "multimodal",
            "supports_video": True,
            "max_frames": 8,
            "api_endpoint": "https://api.nvcf.nvidia.com/v2/nvcf/pexec/functions/..."
        },
        "florence-2": {
            "type": "vision-language",
            "supports_video": False,
            "tasks": ["dense_caption", "region_caption", "object_detection"]
        }
    }


class ProcessingConfig:
    """Configuration for video processing parameters"""
    
    # Video chunking settings
    DEFAULT_CHUNK_DURATION = 10  # seconds
    SUPPORTED_VIDEO_FORMATS = ['.mp4', '.avi', '.mov', '.mkv']
    
    # Detection settings
    DEFAULT_CONFIDENCE_THRESHOLD = 0.25
    DEFAULT_IOU_THRESHOLD = 0.45
    DEFAULT_VID_STRIDE = 5  # Process every 5th frame for tracking
    
    # Caption settings
    DEFAULT_CAPTION_MAX_TOKENS = 1024
    DEFAULT_CAPTION_TEMPERATURE = 0.2
    
    # Processing limits
    MAX_CONCURRENT_JOBS = 4
    MAX_VIDEO_SIZE_MB = 1000
    MAX_CHUNK_FRAMES = 1000


# Example usage configurations
EXAMPLE_CONFIGS = {
    "development": {
        "database": {
            "host": "localhost",
            "port": 5432,
            "database": "video_analysis_dev",
            "user": "dev_user",
            "password": "dev_password"
        },
        "processing": {
            "chunk_duration": 5,
            "confidence_threshold": 0.3,
            "max_concurrent_jobs": 2
        }
    },
    "production": {
        "database": {
            "host": "prod-db-server",
            "port": 5432,
            "database": "video_analysis_prod",
            "user": "prod_user",
            "password": "secure_password"
        },
        "processing": {
            "chunk_duration": 10,
            "confidence_threshold": 0.25,
            "max_concurrent_jobs": 8
        }
    }
}
