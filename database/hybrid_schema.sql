-- Video Analysis Database Schema - Hybrid Approach
-- Combines normalized structure with JSONB flexibility for optimal performance

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- =============================================
-- CORE NORMALIZED TABLES
-- =============================================

-- Videos table: stores original video metadata
CREATE TABLE videos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT,
    duration_seconds DECIMAL(10,3),
    fps DECIMAL(8,3),
    width INTEGER,
    height INTEGER,
    format VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    
    CONSTRAINT unique_video_path UNIQUE(file_path)
);

-- Video chunks table: stores information about video segments
CREATE TABLE video_chunks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    video_id UUID NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    start_time_seconds DECIMAL(10,3) NOT NULL,
    end_time_seconds DECIMAL(10,3) NOT NULL,
    duration_seconds DECIMAL(10,3) NOT NULL,
    width INTEGER,
    height INTEGER,
    fps DECIMAL(8,3),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_chunk_per_video UNIQUE(video_id, chunk_index),
    CONSTRAINT unique_chunk_path UNIQUE(file_path)
);

-- =============================================
-- HYBRID ANALYSIS RESULTS TABLE
-- =============================================

-- Analysis results: hybrid storage with extracted fields + full JSONB
CREATE TABLE analysis_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    video_chunk_id UUID NOT NULL REFERENCES video_chunks(id) ON DELETE CASCADE,
    
    -- Model and execution metadata
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50),
    task_type VARCHAR(50) NOT NULL, -- 'detect', 'track', 'segment'
    prompt_classes TEXT[], -- Array of detection classes used
    
    -- Extracted summary metrics for fast queries
    total_frames INTEGER NOT NULL,
    total_detections INTEGER NOT NULL,
    detected_classes TEXT[], -- Unique classes found in results
    avg_confidence DECIMAL(6,4),
    max_confidence DECIMAL(6,4),
    min_confidence DECIMAL(6,4),
    unique_track_count INTEGER, -- For tracking tasks
    frames_with_detections INTEGER,
    
    -- Processing metadata
    processing_time_seconds DECIMAL(10,3),
    status VARCHAR(50) DEFAULT 'completed', -- 'pending', 'processing', 'completed', 'failed'
    error_message TEXT,
    
    -- Full YOLO JSON data for detailed analysis
    results_data JSONB NOT NULL,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints to ensure data quality
    CONSTRAINT valid_results_data CHECK (
        results_data ? 'frames' AND 
        results_data ? 'total_frames' AND
        results_data ? 'total_detections' AND
        (results_data ->> 'total_frames')::integer = total_frames AND
        (results_data ->> 'total_detections')::integer = total_detections
    ),
    
    CONSTRAINT valid_confidence_range CHECK (
        avg_confidence >= 0 AND avg_confidence <= 1 AND
        max_confidence >= 0 AND max_confidence <= 1 AND
        min_confidence >= 0 AND min_confidence <= 1
    )
);

-- =============================================
-- CAPTION TABLES (Normalized)
-- =============================================

-- Dense captions table: stores AI-generated descriptions
CREATE TABLE dense_captions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    video_chunk_id UUID NOT NULL REFERENCES video_chunks(id) ON DELETE CASCADE,
    caption_text TEXT NOT NULL,
    caption_type VARCHAR(50) DEFAULT 'dense', -- 'dense', 'summary', 'detailed'
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50),
    confidence_score DECIMAL(5,4),
    language VARCHAR(10) DEFAULT 'en',
    processing_time_seconds DECIMAL(10,3),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Full-text search vector
    caption_vector tsvector GENERATED ALWAYS AS (to_tsvector('english', caption_text)) STORED
);

-- Optional: Frame-level captions for detailed analysis
CREATE TABLE frame_captions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    analysis_result_id UUID NOT NULL REFERENCES analysis_results(id) ON DELETE CASCADE,
    frame_index INTEGER NOT NULL,
    caption_text TEXT NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    confidence_score DECIMAL(5,4),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_frame_caption UNIQUE(analysis_result_id, frame_index)
);

-- =============================================
-- MATERIALIZED VIEW FOR COMMON QUERIES
-- =============================================

-- Materialized view for detection statistics (refreshed periodically)
CREATE MATERIALIZED VIEW detection_stats AS
SELECT 
    ar.id as analysis_result_id,
    ar.video_chunk_id,
    v.filename,
    vc.chunk_index,
    ar.model_name,
    ar.task_type,
    unnest(ar.detected_classes) as class_name,
    ar.total_detections,
    ar.avg_confidence,
    -- Extract class-specific stats from JSONB
    (
        SELECT COUNT(*)::integer
        FROM jsonb_array_elements(ar.results_data #> '{frames}') as frame,
             jsonb_array_elements(frame #> '{detections}') as detection
        WHERE detection #>> '{class_name}' = unnest(ar.detected_classes)
    ) as class_detection_count,
    (
        SELECT AVG((detection #>> '{confidence}')::float)::decimal(6,4)
        FROM jsonb_array_elements(ar.results_data #> '{frames}') as frame,
             jsonb_array_elements(frame #> '{detections}') as detection
        WHERE detection #>> '{class_name}' = unnest(ar.detected_classes)
    ) as class_avg_confidence,
    ar.created_at
FROM analysis_results ar
JOIN video_chunks vc ON ar.video_chunk_id = vc.id
JOIN videos v ON vc.video_id = v.id
WHERE ar.status = 'completed';

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Basic indexes on normalized columns
CREATE INDEX idx_videos_filename ON videos(filename);
CREATE INDEX idx_videos_created_at ON videos(created_at);

CREATE INDEX idx_video_chunks_video_id ON video_chunks(video_id);
CREATE INDEX idx_video_chunks_chunk_index ON video_chunks(chunk_index);
CREATE INDEX idx_video_chunks_time_range ON video_chunks(start_time_seconds, end_time_seconds);

-- Analysis results indexes
CREATE INDEX idx_analysis_results_chunk_id ON analysis_results(video_chunk_id);
CREATE INDEX idx_analysis_results_model ON analysis_results(model_name, task_type);
CREATE INDEX idx_analysis_results_status ON analysis_results(status);
CREATE INDEX idx_analysis_results_created_at ON analysis_results(created_at);
CREATE INDEX idx_analysis_results_classes ON analysis_results USING GIN(detected_classes);
CREATE INDEX idx_analysis_results_confidence ON analysis_results(avg_confidence);

-- JSONB indexes for flexible queries
CREATE INDEX idx_analysis_results_jsonb_gin ON analysis_results USING GIN(results_data);
CREATE INDEX idx_analysis_results_frames ON analysis_results USING GIN((results_data #> '{frames}') jsonb_path_ops);
CREATE INDEX idx_analysis_results_detections ON analysis_results USING GIN(
    (results_data #> '{frames}') jsonb_path_ops
) WHERE jsonb_array_length(results_data #> '{frames}') > 0;

-- Specialized indexes for common query patterns
CREATE INDEX idx_analysis_results_class_confidence ON analysis_results 
USING GIN(detected_classes) WHERE avg_confidence > 0.5;

-- Caption indexes
CREATE INDEX idx_dense_captions_chunk_id ON dense_captions(video_chunk_id);
CREATE INDEX idx_dense_captions_model ON dense_captions(model_name);
CREATE INDEX idx_dense_captions_created_at ON dense_captions(created_at);
CREATE INDEX idx_dense_captions_fts ON dense_captions USING GIN(caption_vector);

CREATE INDEX idx_frame_captions_analysis_id ON frame_captions(analysis_result_id);
CREATE INDEX idx_frame_captions_frame_index ON frame_captions(frame_index);

-- Materialized view index
CREATE UNIQUE INDEX idx_detection_stats_unique ON detection_stats(analysis_result_id, class_name);
CREATE INDEX idx_detection_stats_class ON detection_stats(class_name);
CREATE INDEX idx_detection_stats_confidence ON detection_stats(class_avg_confidence);

-- =============================================
-- TRIGGERS AND FUNCTIONS
-- =============================================

-- Function to extract summary statistics from JSONB
CREATE OR REPLACE FUNCTION extract_detection_stats(results_jsonb JSONB)
RETURNS TABLE(
    detected_classes TEXT[],
    avg_confidence DECIMAL(6,4),
    max_confidence DECIMAL(6,4),
    min_confidence DECIMAL(6,4),
    unique_track_count INTEGER,
    frames_with_detections INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH detection_data AS (
        SELECT 
            detection #>> '{class_name}' as class_name,
            (detection #>> '{confidence}')::float as confidence,
            detection #>> '{track_id}' as track_id,
            frame #>> '{frame_index}' as frame_index
        FROM jsonb_array_elements(results_jsonb #> '{frames}') as frame,
             jsonb_array_elements(frame #> '{detections}') as detection
    )
    SELECT 
        ARRAY(SELECT DISTINCT class_name FROM detection_data ORDER BY class_name)::TEXT[],
        AVG(confidence)::DECIMAL(6,4),
        MAX(confidence)::DECIMAL(6,4),
        MIN(confidence)::DECIMAL(6,4),
        COUNT(DISTINCT track_id)::INTEGER,
        COUNT(DISTINCT frame_index)::INTEGER
    FROM detection_data;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Trigger to auto-populate extracted fields
CREATE OR REPLACE FUNCTION update_analysis_stats()
RETURNS TRIGGER AS $$
DECLARE
    stats RECORD;
BEGIN
    -- Extract statistics from JSONB
    SELECT * INTO stats FROM extract_detection_stats(NEW.results_data);
    
    -- Update the extracted fields
    NEW.detected_classes := stats.detected_classes;
    NEW.avg_confidence := stats.avg_confidence;
    NEW.max_confidence := stats.max_confidence;
    NEW.min_confidence := stats.min_confidence;
    NEW.unique_track_count := stats.unique_track_count;
    NEW.frames_with_detections := stats.frames_with_detections;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_analysis_stats
    BEFORE INSERT OR UPDATE OF results_data ON analysis_results
    FOR EACH ROW EXECUTE FUNCTION update_analysis_stats();

-- Update timestamps trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_videos_updated_at BEFORE UPDATE ON videos
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to refresh materialized view
CREATE OR REPLACE FUNCTION refresh_detection_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY detection_stats;
END;
$$ LANGUAGE plpgsql;
