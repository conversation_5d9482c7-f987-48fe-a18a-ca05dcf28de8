-- Hybrid Schema Query Examples
-- Demonstrates the power of combining normalized fields with JSONB flexibility

-- =============================================
-- FAST QUERIES USING EXTRACTED FIELDS
-- =============================================

-- 1. Find videos with specific object classes (using extracted array)
SELECT 
    v.filename,
    ar.model_name,
    ar.detected_classes,
    ar.avg_confidence,
    ar.total_detections
FROM videos v
JOIN video_chunks vc ON v.id = vc.video_id
JOIN analysis_results ar ON vc.id = ar.video_chunk_id
WHERE ar.detected_classes @> ARRAY['cat', 'boat']  -- Contains both cat AND boat
  AND ar.avg_confidence > 0.6
ORDER BY ar.avg_confidence DESC;

-- 2. Get detection statistics by model (using extracted fields)
SELECT 
    model_name,
    task_type,
    COUNT(*) as total_analyses,
    AVG(avg_confidence) as overall_avg_confidence,
    SUM(total_detections) as total_detections_across_all,
    AVG(total_detections::float / total_frames) as avg_detections_per_frame
FROM analysis_results
WHERE status = 'completed'
GROUP BY model_name, task_type
ORDER BY overall_avg_confidence DESC;

-- 3. Find high-performing analysis runs
SELECT 
    v.filename,
    vc.chunk_index,
    ar.model_name,
    ar.detected_classes,
    ar.avg_confidence,
    ar.max_confidence,
    ar.unique_track_count,
    ar.processing_time_seconds
FROM analysis_results ar
JOIN video_chunks vc ON ar.video_chunk_id = vc.id
JOIN videos v ON vc.video_id = v.id
WHERE ar.avg_confidence > 0.8
  AND ar.total_detections > 10
  AND ar.processing_time_seconds < 30
ORDER BY ar.avg_confidence DESC, ar.processing_time_seconds ASC;

-- =============================================
-- JSONB QUERIES FOR DETAILED ANALYSIS
-- =============================================

-- 4. Extract all high-confidence detections from JSONB
SELECT 
    v.filename,
    vc.chunk_index,
    ar.model_name,
    (frame #>> '{frame_index}')::int as frame_index,
    detection #>> '{class_name}' as class_name,
    (detection #>> '{confidence}')::float as confidence,
    detection #> '{bbox}' as bbox,
    detection #>> '{track_id}' as track_id
FROM analysis_results ar
JOIN video_chunks vc ON ar.video_chunk_id = vc.id
JOIN videos v ON vc.video_id = v.id,
     jsonb_array_elements(ar.results_data #> '{frames}') as frame,
     jsonb_array_elements(frame #> '{detections}') as detection
WHERE (detection #>> '{confidence}')::float > 0.9
  AND detection #>> '{class_name}' = 'cat'
ORDER BY (detection #>> '{confidence}')::float DESC;

-- 5. Track object movement across frames (JSONB trajectory analysis)
SELECT 
    v.filename,
    detection #>> '{track_id}' as track_id,
    (frame #>> '{frame_index}')::int as frame_index,
    detection #>> '{class_name}' as class_name,
    (detection #>> '{confidence}')::float as confidence,
    -- Calculate center coordinates
    ((detection #> '{bbox_normalized}' ->> 'x1')::float + 
     (detection #> '{bbox_normalized}' ->> 'x2')::float) / 2 as center_x,
    ((detection #> '{bbox_normalized}' ->> 'y1')::float + 
     (detection #> '{bbox_normalized}' ->> 'y2')::float) / 2 as center_y,
    -- Calculate object size
    ((detection #> '{bbox_normalized}' ->> 'x2')::float - 
     (detection #> '{bbox_normalized}' ->> 'x1')::float) *
    ((detection #> '{bbox_normalized}' ->> 'y2')::float - 
     (detection #> '{bbox_normalized}' ->> 'y1')::float) as normalized_area
FROM analysis_results ar
JOIN video_chunks vc ON ar.video_chunk_id = vc.id
JOIN videos v ON vc.video_id = v.id,
     jsonb_array_elements(ar.results_data #> '{frames}') as frame,
     jsonb_array_elements(frame #> '{detections}') as detection
WHERE detection #>> '{track_id}' = '1'
  AND ar.task_type = 'track'
  AND v.filename = 'cat-on-boat.mp4'
ORDER BY (frame #>> '{frame_index}')::int;

-- 6. Find frames with multiple objects of the same class
SELECT 
    v.filename,
    vc.chunk_index,
    ar.model_name,
    (frame #>> '{frame_index}')::int as frame_index,
    detection #>> '{class_name}' as class_name,
    COUNT(*) as object_count,
    AVG((detection #>> '{confidence}')::float) as avg_confidence,
    array_agg(detection #>> '{track_id}') as track_ids
FROM analysis_results ar
JOIN video_chunks vc ON ar.video_chunk_id = vc.id
JOIN videos v ON vc.video_id = v.id,
     jsonb_array_elements(ar.results_data #> '{frames}') as frame,
     jsonb_array_elements(frame #> '{detections}') as detection
GROUP BY v.filename, vc.chunk_index, ar.model_name, 
         (frame #>> '{frame_index}')::int, detection #>> '{class_name}'
HAVING COUNT(*) > 1
ORDER BY object_count DESC, avg_confidence DESC;

-- =============================================
-- COMBINED QUERIES (EXTRACTED FIELDS + JSONB)
-- =============================================

-- 7. Find videos with specific patterns using both approaches
WITH high_confidence_videos AS (
    -- Fast filter using extracted fields
    SELECT ar.id, v.filename, vc.chunk_index
    FROM analysis_results ar
    JOIN video_chunks vc ON ar.video_chunk_id = vc.id
    JOIN videos v ON vc.video_id = v.id
    WHERE ar.detected_classes @> ARRAY['boat']
      AND ar.avg_confidence > 0.7
      AND ar.total_detections > 5
)
-- Detailed analysis using JSONB
SELECT 
    hcv.filename,
    hcv.chunk_index,
    COUNT(*) as boat_detections,
    AVG((detection #>> '{confidence}')::float) as avg_boat_confidence,
    COUNT(DISTINCT detection #>> '{track_id}') as unique_boat_tracks
FROM high_confidence_videos hcv
JOIN analysis_results ar ON hcv.id = ar.id,
     jsonb_array_elements(ar.results_data #> '{frames}') as frame,
     jsonb_array_elements(frame #> '{detections}') as detection
WHERE detection #>> '{class_name}' = 'boat'
GROUP BY hcv.filename, hcv.chunk_index
ORDER BY avg_boat_confidence DESC;

-- 8. Object co-occurrence analysis
SELECT 
    obj1.class_name as object1,
    obj2.class_name as object2,
    COUNT(*) as co_occurrence_count,
    AVG(obj1.confidence + obj2.confidence) / 2 as avg_combined_confidence
FROM (
    SELECT 
        ar.id as analysis_id,
        (frame #>> '{frame_index}')::int as frame_index,
        detection #>> '{class_name}' as class_name,
        (detection #>> '{confidence}')::float as confidence
    FROM analysis_results ar,
         jsonb_array_elements(ar.results_data #> '{frames}') as frame,
         jsonb_array_elements(frame #> '{detections}') as detection
    WHERE ar.detected_classes && ARRAY['cat', 'boat', 'person']  -- Fast pre-filter
) obj1
JOIN (
    SELECT 
        ar.id as analysis_id,
        (frame #>> '{frame_index}')::int as frame_index,
        detection #>> '{class_name}' as class_name,
        (detection #>> '{confidence}')::float as confidence
    FROM analysis_results ar,
         jsonb_array_elements(ar.results_data #> '{frames}') as frame,
         jsonb_array_elements(frame #> '{detections}') as detection
    WHERE ar.detected_classes && ARRAY['cat', 'boat', 'person']
) obj2 ON obj1.analysis_id = obj2.analysis_id 
       AND obj1.frame_index = obj2.frame_index 
       AND obj1.class_name < obj2.class_name  -- Avoid duplicates
GROUP BY obj1.class_name, obj2.class_name
HAVING COUNT(*) >= 5
ORDER BY co_occurrence_count DESC;

-- =============================================
-- CAPTION + DETECTION CORRELATION
-- =============================================

-- 9. Find videos where captions mention detected objects
SELECT 
    v.filename,
    vc.chunk_index,
    dc.caption_text,
    ar.detected_classes,
    ar.avg_confidence,
    -- Check if caption mentions detected classes
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM unnest(ar.detected_classes) as class_name
            WHERE dc.caption_text ILIKE '%' || class_name || '%'
        ) THEN true 
        ELSE false 
    END as caption_mentions_detected_objects
FROM videos v
JOIN video_chunks vc ON v.id = vc.video_id
JOIN analysis_results ar ON vc.id = ar.video_chunk_id
JOIN dense_captions dc ON vc.id = dc.video_chunk_id
WHERE ar.status = 'completed'
ORDER BY caption_mentions_detected_objects DESC, ar.avg_confidence DESC;

-- 10. Search captions and show corresponding detection summary
SELECT 
    v.filename,
    vc.chunk_index,
    dc.caption_text,
    dc.model_name as caption_model,
    ar.model_name as detection_model,
    ar.detected_classes,
    ar.total_detections,
    ar.avg_confidence,
    ts_rank(dc.caption_vector, plainto_tsquery('english', 'boat water ocean')) as caption_relevance
FROM dense_captions dc
JOIN video_chunks vc ON dc.video_chunk_id = vc.id
JOIN videos v ON vc.video_id = v.id
LEFT JOIN analysis_results ar ON vc.id = ar.video_chunk_id AND ar.status = 'completed'
WHERE dc.caption_vector @@ plainto_tsquery('english', 'boat water ocean')
ORDER BY caption_relevance DESC, ar.avg_confidence DESC NULLS LAST;

-- =============================================
-- PERFORMANCE MONITORING
-- =============================================

-- 11. Model performance comparison
SELECT 
    model_name,
    task_type,
    COUNT(*) as total_runs,
    AVG(processing_time_seconds) as avg_processing_time,
    AVG(total_detections::float / total_frames) as avg_detections_per_frame,
    AVG(avg_confidence) as avg_confidence_score,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY processing_time_seconds) as median_processing_time,
    COUNT(*) FILTER (WHERE status = 'completed') as successful_runs,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_runs
FROM analysis_results
GROUP BY model_name, task_type
ORDER BY avg_confidence_score DESC, avg_processing_time ASC;

-- 12. Storage usage analysis
SELECT 
    'analysis_results' as table_name,
    COUNT(*) as row_count,
    pg_size_pretty(pg_total_relation_size('analysis_results')) as total_size,
    pg_size_pretty(AVG(pg_column_size(results_data))) as avg_jsonb_size
FROM analysis_results
UNION ALL
SELECT 
    'dense_captions' as table_name,
    COUNT(*) as row_count,
    pg_size_pretty(pg_total_relation_size('dense_captions')) as total_size,
    pg_size_pretty(AVG(pg_column_size(caption_text))) as avg_text_size
FROM dense_captions;

-- =============================================
-- MATERIALIZED VIEW QUERIES
-- =============================================

-- 13. Use materialized view for fast class statistics
SELECT 
    class_name,
    COUNT(DISTINCT analysis_result_id) as videos_with_class,
    SUM(class_detection_count) as total_detections,
    AVG(class_avg_confidence) as overall_avg_confidence,
    MIN(class_avg_confidence) as min_confidence,
    MAX(class_avg_confidence) as max_confidence
FROM detection_stats
GROUP BY class_name
ORDER BY total_detections DESC;

-- 14. Time-based analysis using materialized view
SELECT 
    DATE_TRUNC('day', created_at) as analysis_date,
    COUNT(DISTINCT analysis_result_id) as analyses_per_day,
    COUNT(DISTINCT class_name) as unique_classes_detected,
    SUM(class_detection_count) as total_detections_per_day,
    AVG(class_avg_confidence) as daily_avg_confidence
FROM detection_stats
GROUP BY DATE_TRUNC('day', created_at)
ORDER BY analysis_date DESC;
