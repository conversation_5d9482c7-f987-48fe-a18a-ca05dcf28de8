"""
Video Analysis Database Interface
Provides high-level methods for storing and retrieving video analysis data
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from uuid import UUID, uuid4
from dataclasses import dataclass
from pathlib import Path

import psycopg2
from psycopg2.extras import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>
from psycopg2.extensions import register_adapter
import psycopg2.extras

# Register UUID adapter
psycopg2.extras.register_uuid()
register_adapter(dict, Json)

logger = logging.getLogger(__name__)


@dataclass
class VideoInfo:
    """Video metadata container"""
    filename: str
    file_path: str
    file_size: Optional[int] = None
    duration_seconds: Optional[float] = None
    fps: Optional[float] = None
    width: Optional[int] = None
    height: Optional[int] = None
    format: Optional[str] = None
    metadata: Optional[Dict] = None


@dataclass
class VideoChunkInfo:
    """Video chunk metadata container"""
    video_id: UUID
    chunk_index: int
    filename: str
    file_path: str
    start_time_seconds: float
    end_time_seconds: float
    duration_seconds: float
    width: Optional[int] = None
    height: Optional[int] = None
    total_frames: Optional[int] = None
    fps: Optional[float] = None


@dataclass
class AnalysisRunInfo:
    """Analysis run metadata container"""
    video_chunk_id: UUID
    model_name: str
    task_type: str
    prompt_classes: List[str]
    model_version: Optional[str] = None
    parameters: Optional[Dict] = None
    total_frames: Optional[int] = None
    total_detections: Optional[int] = None
    processing_time_seconds: Optional[float] = None


class VideoAnalysisDB:
    """Database interface for video analysis data"""
    
    def __init__(self, connection_string: str):
        """
        Initialize database connection
        
        Args:
            connection_string: PostgreSQL connection string
                e.g., "postgresql://user:password@localhost:5432/video_analysis"
        """
        self.connection_string = connection_string
        self._connection = None
    
    def connect(self):
        """Establish database connection"""
        try:
            self._connection = psycopg2.connect(
                self.connection_string,
                cursor_factory=RealDictCursor
            )
            self._connection.autocommit = False
            logger.info("Connected to video analysis database")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise
    
    def disconnect(self):
        """Close database connection"""
        if self._connection:
            self._connection.close()
            self._connection = None
            logger.info("Disconnected from database")
    
    def __enter__(self):
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self._connection.rollback()
        else:
            self._connection.commit()
        self.disconnect()
    
    # =============================================
    # VIDEO MANAGEMENT
    # =============================================
    
    def insert_video(self, video_info: VideoInfo) -> UUID:
        """Insert a new video record"""
        with self._connection.cursor() as cursor:
            cursor.execute("""
                INSERT INTO videos (filename, file_path, file_size, duration_seconds, 
                                  fps, width, height, format, metadata)
                VALUES (%(filename)s, %(file_path)s, %(file_size)s, %(duration_seconds)s,
                       %(fps)s, %(width)s, %(height)s, %(format)s, %(metadata)s)
                ON CONFLICT (file_path) DO UPDATE SET
                    filename = EXCLUDED.filename,
                    file_size = EXCLUDED.file_size,
                    duration_seconds = EXCLUDED.duration_seconds,
                    fps = EXCLUDED.fps,
                    width = EXCLUDED.width,
                    height = EXCLUDED.height,
                    format = EXCLUDED.format,
                    metadata = EXCLUDED.metadata,
                    updated_at = NOW()
                RETURNING id
            """, {
                'filename': video_info.filename,
                'file_path': video_info.file_path,
                'file_size': video_info.file_size,
                'duration_seconds': video_info.duration_seconds,
                'fps': video_info.fps,
                'width': video_info.width,
                'height': video_info.height,
                'format': video_info.format,
                'metadata': video_info.metadata or {}
            })
            video_id = cursor.fetchone()['id']
            self._connection.commit()
            logger.info(f"Inserted video: {video_info.filename} with ID: {video_id}")
            return video_id
    
    def insert_video_chunk(self, chunk_info: VideoChunkInfo) -> UUID:
        """Insert a new video chunk record"""
        with self._connection.cursor() as cursor:
            cursor.execute("""
                INSERT INTO video_chunks (video_id, chunk_index, filename, file_path,
                                        start_time_seconds, end_time_seconds, duration_seconds,
                                        width, height, total_frames, fps)
                VALUES (%(video_id)s, %(chunk_index)s, %(filename)s, %(file_path)s,
                       %(start_time_seconds)s, %(end_time_seconds)s, %(duration_seconds)s,
                       %(width)s, %(height)s, %(total_frames)s, %(fps)s)
                ON CONFLICT (file_path) DO UPDATE SET
                    chunk_index = EXCLUDED.chunk_index,
                    start_time_seconds = EXCLUDED.start_time_seconds,
                    end_time_seconds = EXCLUDED.end_time_seconds,
                    duration_seconds = EXCLUDED.duration_seconds,
                    width = EXCLUDED.width,
                    height = EXCLUDED.height,
                    total_frames = EXCLUDED.total_frames,
                    fps = EXCLUDED.fps
                RETURNING id
            """, {
                'video_id': chunk_info.video_id,
                'chunk_index': chunk_info.chunk_index,
                'filename': chunk_info.filename,
                'file_path': chunk_info.file_path,
                'start_time_seconds': chunk_info.start_time_seconds,
                'end_time_seconds': chunk_info.end_time_seconds,
                'duration_seconds': chunk_info.duration_seconds,
                'width': chunk_info.width,
                'height': chunk_info.height,
                'total_frames': chunk_info.total_frames,
                'fps': chunk_info.fps
            })
            chunk_id = cursor.fetchone()['id']
            self._connection.commit()
            logger.info(f"Inserted video chunk: {chunk_info.filename} with ID: {chunk_id}")
            return chunk_id
    
    # =============================================
    # ANALYSIS MANAGEMENT
    # =============================================
    
    def create_analysis_run(self, analysis_info: AnalysisRunInfo) -> UUID:
        """Create a new analysis run record"""
        with self._connection.cursor() as cursor:
            cursor.execute("""
                INSERT INTO analysis_runs (video_chunk_id, model_name, model_version, task_type,
                                         prompt_classes, parameters, total_frames, total_detections,
                                         processing_time_seconds, status)
                VALUES (%(video_chunk_id)s, %(model_name)s, %(model_version)s, %(task_type)s,
                       %(prompt_classes)s, %(parameters)s, %(total_frames)s, %(total_detections)s,
                       %(processing_time_seconds)s, 'completed')
                RETURNING id
            """, {
                'video_chunk_id': analysis_info.video_chunk_id,
                'model_name': analysis_info.model_name,
                'model_version': analysis_info.model_version,
                'task_type': analysis_info.task_type,
                'prompt_classes': analysis_info.prompt_classes,
                'parameters': analysis_info.parameters or {},
                'total_frames': analysis_info.total_frames,
                'total_detections': analysis_info.total_detections,
                'processing_time_seconds': analysis_info.processing_time_seconds
            })
            run_id = cursor.fetchone()['id']
            self._connection.commit()
            logger.info(f"Created analysis run with ID: {run_id}")
            return run_id
    
    def store_yolo_results(self, analysis_run_id: UUID, yolo_json_data: Dict) -> int:
        """
        Store YOLO detection results from JSON format
        
        Args:
            analysis_run_id: ID of the analysis run
            yolo_json_data: JSON data from YOLO inference (as loaded from results_to_json)
            
        Returns:
            Number of detections stored
        """
        total_detections = 0
        
        with self._connection.cursor() as cursor:
            # Process each frame
            for frame_data in yolo_json_data.get('frames', []):
                # Insert frame record
                cursor.execute("""
                    INSERT INTO frames (analysis_run_id, frame_index, width, height, num_detections)
                    VALUES (%(analysis_run_id)s, %(frame_index)s, %(width)s, %(height)s, %(num_detections)s)
                    RETURNING id
                """, {
                    'analysis_run_id': analysis_run_id,
                    'frame_index': frame_data['frame_index'],
                    'width': frame_data['image_shape']['width'],
                    'height': frame_data['image_shape']['height'],
                    'num_detections': frame_data.get('num_detections', len(frame_data.get('detections', [])))
                })
                frame_id = cursor.fetchone()['id']
                
                # Insert detection records
                for detection in frame_data.get('detections', []):
                    # Ensure object class exists
                    cursor.execute("""
                        INSERT INTO object_classes (class_name, class_id)
                        VALUES (%(class_name)s, %(class_id)s)
                        ON CONFLICT (class_name) DO NOTHING
                    """, {
                        'class_name': detection['class_name'],
                        'class_id': detection['class_id']
                    })
                    
                    # Insert detection
                    cursor.execute("""
                        INSERT INTO detections (
                            frame_id, class_name, confidence,
                            bbox_x1, bbox_y1, bbox_x2, bbox_y2,
                            bbox_norm_x1, bbox_norm_y1, bbox_norm_x2, bbox_norm_y2,
                            track_id, has_mask, mask_area, mask_shape
                        ) VALUES (
                            %(frame_id)s, %(class_name)s, %(confidence)s,
                            %(bbox_x1)s, %(bbox_y1)s, %(bbox_x2)s, %(bbox_y2)s,
                            %(bbox_norm_x1)s, %(bbox_norm_y1)s, %(bbox_norm_x2)s, %(bbox_norm_y2)s,
                            %(track_id)s, %(has_mask)s, %(mask_area)s, %(mask_shape)s
                        )
                    """, {
                        'frame_id': frame_id,
                        'class_name': detection['class_name'],
                        'confidence': detection['confidence'],
                        'bbox_x1': detection['bbox']['x1'],
                        'bbox_y1': detection['bbox']['y1'],
                        'bbox_x2': detection['bbox']['x2'],
                        'bbox_y2': detection['bbox']['y2'],
                        'bbox_norm_x1': detection['bbox_normalized']['x1'],
                        'bbox_norm_y1': detection['bbox_normalized']['y1'],
                        'bbox_norm_x2': detection['bbox_normalized']['x2'],
                        'bbox_norm_y2': detection['bbox_normalized']['y2'],
                        'track_id': detection.get('track_id'),
                        'has_mask': detection.get('mask', {}).get('has_mask', False),
                        'mask_area': detection.get('mask', {}).get('area'),
                        'mask_shape': detection.get('mask', {}).get('shape')
                    })
                    total_detections += 1
            
            self._connection.commit()
            logger.info(f"Stored {total_detections} detections for analysis run {analysis_run_id}")
            return total_detections
    
    def store_dense_caption(self, video_chunk_id: UUID, caption_text: str, 
                          model_name: str, model_version: str = None,
                          confidence_score: float = None, 
                          processing_time_seconds: float = None) -> UUID:
        """Store a dense caption for a video chunk"""
        with self._connection.cursor() as cursor:
            cursor.execute("""
                INSERT INTO dense_captions (video_chunk_id, caption_text, model_name, 
                                          model_version, confidence_score, processing_time_seconds)
                VALUES (%(video_chunk_id)s, %(caption_text)s, %(model_name)s,
                       %(model_version)s, %(confidence_score)s, %(processing_time_seconds)s)
                RETURNING id
            """, {
                'video_chunk_id': video_chunk_id,
                'caption_text': caption_text,
                'model_name': model_name,
                'model_version': model_version,
                'confidence_score': confidence_score,
                'processing_time_seconds': processing_time_seconds
            })
            caption_id = cursor.fetchone()['id']
            self._connection.commit()
            logger.info(f"Stored dense caption with ID: {caption_id}")
            return caption_id

    # =============================================
    # QUERY METHODS
    # =============================================

    def get_video_by_path(self, file_path: str) -> Optional[Dict]:
        """Get video record by file path"""
        with self._connection.cursor() as cursor:
            cursor.execute("""
                SELECT * FROM videos WHERE file_path = %(file_path)s
            """, {'file_path': file_path})
            return cursor.fetchone()

    def get_video_chunks(self, video_id: UUID) -> List[Dict]:
        """Get all chunks for a video"""
        with self._connection.cursor() as cursor:
            cursor.execute("""
                SELECT * FROM video_chunks
                WHERE video_id = %(video_id)s
                ORDER BY chunk_index
            """, {'video_id': video_id})
            return cursor.fetchall()

    def get_chunk_by_path(self, file_path: str) -> Optional[Dict]:
        """Get video chunk by file path"""
        with self._connection.cursor() as cursor:
            cursor.execute("""
                SELECT * FROM video_chunks WHERE file_path = %(file_path)s
            """, {'file_path': file_path})
            return cursor.fetchone()

    def get_analysis_runs(self, video_chunk_id: UUID) -> List[Dict]:
        """Get all analysis runs for a video chunk"""
        with self._connection.cursor() as cursor:
            cursor.execute("""
                SELECT * FROM analysis_runs
                WHERE video_chunk_id = %(video_chunk_id)s
                ORDER BY created_at DESC
            """, {'video_chunk_id': video_chunk_id})
            return cursor.fetchall()

    def get_detections_by_class(self, analysis_run_id: UUID, class_name: str,
                               min_confidence: float = 0.0) -> List[Dict]:
        """Get detections filtered by class and confidence"""
        with self._connection.cursor() as cursor:
            cursor.execute("""
                SELECT d.*, f.frame_index, f.timestamp_seconds
                FROM detections d
                JOIN frames f ON d.frame_id = f.id
                WHERE f.analysis_run_id = %(analysis_run_id)s
                  AND d.class_name = %(class_name)s
                  AND d.confidence >= %(min_confidence)s
                ORDER BY f.frame_index
            """, {
                'analysis_run_id': analysis_run_id,
                'class_name': class_name,
                'min_confidence': min_confidence
            })
            return cursor.fetchall()

    def get_track_trajectory(self, analysis_run_id: UUID, track_id: int) -> List[Dict]:
        """Get trajectory of a tracked object"""
        with self._connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    d.*,
                    f.frame_index,
                    f.timestamp_seconds,
                    (d.bbox_x1 + d.bbox_x2) / 2 as center_x,
                    (d.bbox_y1 + d.bbox_y2) / 2 as center_y,
                    (d.bbox_x2 - d.bbox_x1) * (d.bbox_y2 - d.bbox_y1) as bbox_area
                FROM detections d
                JOIN frames f ON d.frame_id = f.id
                WHERE f.analysis_run_id = %(analysis_run_id)s
                  AND d.track_id = %(track_id)s
                ORDER BY f.frame_index
            """, {
                'analysis_run_id': analysis_run_id,
                'track_id': track_id
            })
            return cursor.fetchall()

    def search_captions(self, search_text: str, limit: int = 10) -> List[Dict]:
        """Search dense captions using full-text search"""
        with self._connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    dc.*,
                    v.filename,
                    vc.chunk_index,
                    ts_rank(dc.caption_vector, plainto_tsquery('english', %(search_text)s)) as rank
                FROM dense_captions dc
                JOIN video_chunks vc ON dc.video_chunk_id = vc.id
                JOIN videos v ON vc.video_id = v.id
                WHERE dc.caption_vector @@ plainto_tsquery('english', %(search_text)s)
                ORDER BY rank DESC
                LIMIT %(limit)s
            """, {
                'search_text': search_text,
                'limit': limit
            })
            return cursor.fetchall()

    def get_video_summary(self, video_id: UUID) -> Dict:
        """Get comprehensive summary of video analysis"""
        with self._connection.cursor() as cursor:
            # Get video info
            cursor.execute("SELECT * FROM videos WHERE id = %(video_id)s", {'video_id': video_id})
            video = cursor.fetchone()

            if not video:
                return None

            # Get chunk count
            cursor.execute("""
                SELECT COUNT(*) as chunk_count FROM video_chunks WHERE video_id = %(video_id)s
            """, {'video_id': video_id})
            chunk_count = cursor.fetchone()['chunk_count']

            # Get analysis summary
            cursor.execute("""
                SELECT
                    ar.model_name,
                    ar.task_type,
                    COUNT(*) as run_count,
                    SUM(ar.total_detections) as total_detections,
                    AVG(ar.processing_time_seconds) as avg_processing_time
                FROM analysis_runs ar
                JOIN video_chunks vc ON ar.video_chunk_id = vc.id
                WHERE vc.video_id = %(video_id)s
                GROUP BY ar.model_name, ar.task_type
            """, {'video_id': video_id})
            analysis_summary = cursor.fetchall()

            # Get caption count
            cursor.execute("""
                SELECT COUNT(*) as caption_count
                FROM dense_captions dc
                JOIN video_chunks vc ON dc.video_chunk_id = vc.id
                WHERE vc.video_id = %(video_id)s
            """, {'video_id': video_id})
            caption_count = cursor.fetchone()['caption_count']

            return {
                'video': dict(video),
                'chunk_count': chunk_count,
                'analysis_summary': [dict(row) for row in analysis_summary],
                'caption_count': caption_count
            }
