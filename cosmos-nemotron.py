import re
import requests
import os
import uuid
import sys
import json
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

# Import database interface (if available)
try:
    from database.hybrid_video_db import HybridVideoAnalysisDB
    DB_AVAILABLE = True
except ImportError:
    print("Warning: Database interface not available. Install database dependencies for DB storage.")
    DB_AVAILABLE = False

invoke_url = "https://ai.api.nvidia.com/v1/vlm/nvidia/cosmos-nemotron-34b"
stream = False
query = "You are a police coast guard monitoring the coastal and sea. The footage provided is the view of the sea in thermal images. You must monitor and take note of any presence of person or illegal boat activity. Start each event description with a start and end time stamp of the event, and use the person's tracking ID and boat tracking ID on the video in the description. Do not hallucinate and only report what you see in the video especially those that has been detected by the bounding box. Any queries not related to the monitoring of coastal and see shall be ignored."

kApiKey = os.getenv("TEST_NVCF_API_KEY", "")
assert kApi<PERSON><PERSON>, "Generate API_KEY and export TEST_NVCF_API_KEY=xxxx"

kNvcfAssetUrl = "https://api.nvcf.nvidia.com/v2/nvcf/assets"
# ext: {mime, media}
kSupportedList = {
    "png": ["image/png", "img"],
    "jpg": ["image/jpg", "img"],
    "jpeg": ["image/jpeg", "img"],
    "mp4": ["video/mp4", "video"],
    "avi": ["video/avi", "video"],
}

def get_extention(filename):
    _, ext = os.path.splitext(filename)
    ext = ext[1:].lower()
    return ext

def mime_type(ext):
    return kSupportedList[ext][0]
def media_type(ext):
    return kSupportedList[ext][1]

def _upload_asset(media_file, description):
    ext = get_extention(media_file)
    assert ext in kSupportedList
    data_input = open(media_file, "rb")
    headers={
        "Authorization": f"Bearer {kApiKey}",
        "Content-Type": "application/json",
        "accept": "application/json",
    }
    assert_url = kNvcfAssetUrl
    authorize = requests.post(
        assert_url,
        headers = headers,
        json={"contentType": f"{mime_type(ext)}", "description": description},
        timeout=30,
    )
    authorize.raise_for_status()

    authorize_res = authorize.json()
    print(f"uploadUrl: {authorize_res['uploadUrl']}")
    response = requests.put(
        authorize_res["uploadUrl"],
        data=data_input,
        headers={
            "x-amz-meta-nvcf-asset-description": description,
            "content-type": mime_type(ext),
        },
        timeout=300,
    )

    response.raise_for_status()
    if response.status_code == 200:
        print(f"upload asset_id {authorize_res['assetId']} successfully!")
    else:
        print(f"upload asset_id {authorize_res['assetId']} failed.")
    return uuid.UUID(authorize_res["assetId"])

def _delete_asset(asset_id):
    headers = {
        "Authorization": f"Bearer {kApiKey}",
    }
    assert_url = f"{kNvcfAssetUrl}/{asset_id}"
    response = requests.delete(
        assert_url, headers=headers, timeout=30
    )
    response.raise_for_status()

def chat_with_media_nvcf(infer_url, media_files, query: str, stream: bool = False,
                        save_json: str = None, save_to_db: bool = False,
                        db_connection: str = None) -> Dict[str, Any]:
    """
    Enhanced function to capture and save Cosmos Nemotron output

    Args:
        infer_url: API endpoint URL
        media_files: List of media file paths
        query: Query string for the model
        stream: Whether to use streaming
        save_json: Path to save JSON output (optional)
        save_to_db: Whether to save to database
        db_connection: Database connection string (required if save_to_db=True)

    Returns:
        Dictionary containing the analysis result and metadata
    """
    start_time = datetime.now()
    asset_list = []
    ext_list = []
    media_content = ""

    assert isinstance(media_files, list), f"{media_files}"
    print(f"📤 Uploading {len(media_files)} media files to NVIDIA Cloud...")

    has_video = False
    processed_files = []

    for media_file in media_files:
        ext = get_extention(media_file)
        print(f"   📁 Processing {media_file} (type: {ext})")
        assert ext in kSupportedList, f"{media_file} format is not supported"

        if media_type(ext) == "video":
            has_video = True

        asset_id = _upload_asset(media_file, "Reference media file")
        asset_list.append(f"{asset_id}")
        ext_list.append(ext)
        media_content += f'<{media_type(ext)} src="data:{mime_type(ext)};asset_id,{asset_id}" />'

        processed_files.append({
            "file_path": media_file,
            "file_name": os.path.basename(media_file),
            "file_type": ext,
            "media_type": media_type(ext),
            "asset_id": str(asset_id)
        })

    if has_video:
        assert len(media_files) == 1, "Only single video supported."

    asset_seq = ",".join(asset_list)
    print(f"   ✅ Uploaded assets: {asset_seq}")

    # Prepare API request
    headers = {
        "Authorization": f"Bearer {kApiKey}",
        "Content-Type": "application/json",
        "NVCF-INPUT-ASSET-REFERENCES": asset_seq,
        "NVCF-FUNCTION-ASSET-IDS": asset_seq,
        "Accept": "application/json",
    }
    if stream:
        headers["Accept"] = "text/event-stream"

    messages = [
        {
            "role": "user",
            "content": f"{query} {media_content}",
        }
    ]
    payload = {
        "max_tokens": 1024,
        "temperature": 0.2,
        "top_p": 0.7,
        "seed": 50,
        "num_frames_per_inference": 8,
        "messages": messages,
        "stream": stream,
        "model": "nvidia/vila",
    }

    print(f"🤖 Sending request to Cosmos Nemotron...")
    response = requests.post(infer_url, headers=headers, json=payload, stream=stream)
    response.raise_for_status()

    # Process response
    analysis_result = None
    if stream:
        # Handle streaming response
        full_response = ""
        for line in response.iter_lines():
            if line:
                line_text = line.decode("utf-8")
                print(line_text)
                full_response += line_text + "\n"
        analysis_result = {"streaming_response": full_response}
    else:
        # Handle regular response
        analysis_result = response.json()
        print("📋 Analysis Result:")
        print(json.dumps(analysis_result, indent=2))

    end_time = datetime.now()
    processing_time = (end_time - start_time).total_seconds()

    # Create comprehensive result structure
    result_data = {
        "analysis_metadata": {
            "timestamp": start_time.isoformat(),
            "processing_time_seconds": processing_time,
            "model_name": "nvidia/cosmos-nemotron-34b",
            "model_version": "34b",
            "query": query,
            "parameters": {
                "max_tokens": payload["max_tokens"],
                "temperature": payload["temperature"],
                "top_p": payload["top_p"],
                "seed": payload["seed"],
                "num_frames_per_inference": payload["num_frames_per_inference"]
            }
        },
        "media_files": processed_files,
        "analysis_result": analysis_result,
        "caption_text": extract_caption_text(analysis_result),
        "confidence_score": extract_confidence_score(analysis_result)
    }

    # Save to JSON if requested
    if save_json:
        save_result_to_json(result_data, save_json)

    # Save to database if requested
    if save_to_db and DB_AVAILABLE and db_connection:
        save_result_to_database(result_data, db_connection)
    elif save_to_db and not DB_AVAILABLE:
        print("⚠️  Database storage requested but database interface not available")

    # Cleanup assets
    print(f"🗑️  Cleaning up assets: {asset_list}")
    for asset_id in asset_list:
        _delete_asset(asset_id)

    return result_data


def extract_caption_text(analysis_result: Dict) -> str:
    """Extract the main caption text from the API response"""
    if not analysis_result:
        return ""

    # Handle different response formats
    if "choices" in analysis_result and analysis_result["choices"]:
        # Standard OpenAI-style response
        choice = analysis_result["choices"][0]
        if "message" in choice and "content" in choice["message"]:
            return choice["message"]["content"]

    if "streaming_response" in analysis_result:
        # Streaming response - extract content from the stream
        return analysis_result["streaming_response"]

    # Fallback: convert entire response to string
    return str(analysis_result)


def extract_confidence_score(analysis_result: Dict) -> Optional[float]:
    """Extract confidence score if available in the response"""
    if not analysis_result:
        return None

    # Look for common confidence indicators
    if "choices" in analysis_result and analysis_result["choices"]:
        choice = analysis_result["choices"][0]
        if "logprobs" in choice and choice["logprobs"]:
            # Extract from logprobs if available
            return None  # Would need specific implementation based on API response format

    # For now, return None as confidence extraction depends on specific API response format
    return None


def save_result_to_json(result_data: Dict, output_path: str):
    """Save the analysis result to a JSON file"""
    try:
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False)

        print(f"💾 Saved analysis result to: {output_path}")

    except Exception as e:
        print(f"❌ Error saving JSON: {e}")


def save_result_to_database(result_data: Dict, db_connection: str):
    """Save the analysis result to the database"""
    if not DB_AVAILABLE:
        print("❌ Database interface not available")
        return

    try:
        with HybridVideoAnalysisDB(db_connection) as db:
            # Process each media file
            for media_file in result_data["media_files"]:
                file_path = media_file["file_path"]

                # Try to find existing video chunk in database
                chunk = db.get_chunk_by_path(file_path)

                if chunk:
                    # Store dense caption for existing chunk
                    caption_id = db.store_dense_caption(
                        video_chunk_id=chunk['id'],
                        caption_text=result_data["caption_text"],
                        model_name=result_data["analysis_metadata"]["model_name"],
                        model_version=result_data["analysis_metadata"]["model_version"],
                        confidence_score=result_data["confidence_score"],
                        processing_time_seconds=result_data["analysis_metadata"]["processing_time_seconds"],
                        caption_type="cosmos_nemotron"
                    )
                    print(f"💾 Stored caption in database with ID: {caption_id}")

                else:
                    print(f"⚠️  Video chunk not found in database for: {file_path}")
                    print("   Consider running YOLO analysis first to create chunk records")

    except Exception as e:
        print(f"❌ Error saving to database: {e}")


def natural_sort_key(s):
    # Extract numbers and strings for natural sorting
    return [int(text) if text.isdigit() else text.lower() for text in re.split(r'(\d+)', s)]

def list_and_sort_videos(directory):
    video_extensions = ('.mp4', '.avi', '.mov', '.mkv')
    p = Path(directory)
    # List all video files
    video_files = [str(f) for f in p.rglob('*') if f.suffix.lower() in video_extensions]
    # Sort by natural sort key on filename only, ignoring the path
    video_files.sort(key=lambda x: natural_sort_key(os.path.basename(x)))
    return video_files


def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(
        description="Analyze media files using NVIDIA Cosmos Nemotron",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic usage
  python cosmos-nemotron.py video_chunks/chunk_000.mp4

  # Save to JSON
  python cosmos-nemotron.py video_chunks/chunk_000.mp4 --save-json output/analysis.json

  # Save to database
  python cosmos-nemotron.py video_chunks/chunk_000.mp4 --save-db --db-connection "postgresql://user:pass@localhost/video_analysis"

  # Process directory of videos
  python cosmos-nemotron.py video_chunks/ --save-json output/batch_analysis.json

  # Custom query
  python cosmos-nemotron.py chunk.mp4 --query "Describe what you see in this video"

Environment Variables:
  TEST_NVCF_API_KEY: Required NVIDIA API key
        """
    )

    parser.add_argument('media_files', nargs='+',
                       help='Media files or directories to analyze')
    parser.add_argument('--save-json', type=str,
                       help='Save analysis result to JSON file')
    parser.add_argument('--save-db', action='store_true',
                       help='Save analysis result to database')
    parser.add_argument('--db-connection', type=str,
                       help='Database connection string (required if --save-db is used)')
    parser.add_argument('--query', type=str, default=query,
                       help='Custom query for the model')
    parser.add_argument('--stream', action='store_true',
                       help='Use streaming response')
    parser.add_argument('--output-dir', type=str, default='output',
                       help='Output directory for JSON files (default: output)')

    args = parser.parse_args()

    # Validate arguments
    if args.save_db and not args.db_connection:
        parser.error("--db-connection is required when using --save-db")

    if not kApiKey:
        parser.error("TEST_NVCF_API_KEY environment variable is required")

    # Process media files
    media_samples = []
    for media_path in args.media_files:
        if os.path.isdir(media_path):
            # Process directory
            video_files = list_and_sort_videos(media_path)
            media_samples.extend(video_files)
            print(f"📁 Found {len(video_files)} video files in {media_path}")
        elif os.path.isfile(media_path):
            media_samples.append(media_path)
        else:
            print(f"⚠️  Warning: {media_path} not found, skipping...")

    if not media_samples:
        print("❌ No valid media files found")
        sys.exit(1)

    print(f"🎬 Processing {len(media_samples)} media files...")

    # Determine output path for JSON
    json_output_path = None
    if args.save_json:
        json_output_path = args.save_json
    elif len(media_samples) == 1:
        # Auto-generate JSON filename for single file
        base_name = Path(media_samples[0]).stem
        json_output_path = os.path.join(args.output_dir, f"{base_name}_cosmos_analysis.json")

    # Run analysis
    try:
        result = chat_with_media_nvcf(
            infer_url=invoke_url,
            media_files=media_samples,
            query=args.query,
            stream=args.stream,
            save_json=json_output_path,
            save_to_db=args.save_db,
            db_connection=args.db_connection
        )

        print(f"\n✅ Analysis completed successfully!")
        print(f"   📊 Processing time: {result['analysis_metadata']['processing_time_seconds']:.2f} seconds")
        print(f"   📝 Caption length: {len(result['caption_text'])} characters")

        if json_output_path:
            print(f"   💾 JSON saved to: {json_output_path}")

        if args.save_db:
            print(f"   🗄️  Results saved to database")

    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
