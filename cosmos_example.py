#!/usr/bin/env python3
"""
Example script showing different ways to use the enhanced Cosmos Nemotron analysis
"""

import os
import json
from pathlib import Path

# Import the enhanced cosmos nemotron function
from cosmos_nemotron import chat_with_media_nvcf

# Database connection (adjust for your setup)
DB_CONNECTION = "postgresql://username:password@localhost:5432/video_analysis"

def example_basic_analysis():
    """Example 1: Basic analysis with console output"""
    print("🔍 Example 1: Basic Analysis")
    
    video_file = "video_chunks/chunk_000.mp4"
    if not os.path.exists(video_file):
        print(f"⚠️  Video file not found: {video_file}")
        return
    
    query = "Describe what you see in this video footage."
    
    result = chat_with_media_nvcf(
        infer_url="https://ai.api.nvidia.com/v1/vlm/nvidia/cosmos-nemotron-34b",
        media_files=[video_file],
        query=query,
        stream=False
    )
    
    print(f"✅ Analysis completed in {result['analysis_metadata']['processing_time_seconds']:.2f} seconds")
    print(f"📝 Caption: {result['caption_text'][:200]}...")


def example_save_to_json():
    """Example 2: Save analysis result to JSON file"""
    print("\n💾 Example 2: Save to JSON")
    
    video_file = "video_chunks/chunk_000.mp4"
    if not os.path.exists(video_file):
        print(f"⚠️  Video file not found: {video_file}")
        return
    
    output_json = "output/chunk_000_cosmos_analysis.json"
    
    query = """You are analyzing maritime surveillance footage. 
    Identify and describe any vessels, people, or suspicious activities. 
    Include timestamps and tracking information if visible."""
    
    result = chat_with_media_nvcf(
        infer_url="https://ai.api.nvidia.com/v1/vlm/nvidia/cosmos-nemotron-34b",
        media_files=[video_file],
        query=query,
        stream=False,
        save_json=output_json
    )
    
    print(f"✅ Analysis saved to: {output_json}")
    
    # Load and display the saved JSON structure
    with open(output_json, 'r') as f:
        saved_data = json.load(f)
    
    print("📋 JSON Structure:")
    print(f"  - Analysis metadata: {len(saved_data['analysis_metadata'])} fields")
    print(f"  - Media files: {len(saved_data['media_files'])} files")
    print(f"  - Caption text: {len(saved_data['caption_text'])} characters")
    print(f"  - Processing time: {saved_data['analysis_metadata']['processing_time_seconds']:.2f}s")


def example_save_to_database():
    """Example 3: Save analysis result to database"""
    print("\n🗄️  Example 3: Save to Database")
    
    video_file = "video_chunks/chunk_000.mp4"
    if not os.path.exists(video_file):
        print(f"⚠️  Video file not found: {video_file}")
        return
    
    query = """Analyze this coastal surveillance video. Report any:
    1. Boats or vessels (with approximate size and type)
    2. People or human activity
    3. Unusual or suspicious behavior
    4. Environmental conditions
    
    Use tracking IDs from bounding boxes when available."""
    
    try:
        result = chat_with_media_nvcf(
            infer_url="https://ai.api.nvidia.com/v1/vlm/nvidia/cosmos-nemotron-34b",
            media_files=[video_file],
            query=query,
            stream=False,
            save_to_db=True,
            db_connection=DB_CONNECTION
        )
        
        print(f"✅ Analysis saved to database")
        print(f"📊 Caption stored with {len(result['caption_text'])} characters")
        
    except Exception as e:
        print(f"❌ Database save failed: {e}")
        print("   Make sure the video chunk exists in the database first")
        print("   (Run YOLO analysis to create chunk records)")


def example_batch_processing():
    """Example 4: Process multiple files"""
    print("\n📦 Example 4: Batch Processing")
    
    video_dir = "video_chunks"
    if not os.path.exists(video_dir):
        print(f"⚠️  Video directory not found: {video_dir}")
        return
    
    # Get first 3 video files for demo
    video_files = []
    for ext in ['.mp4', '.avi', '.mov']:
        video_files.extend(Path(video_dir).glob(f"*{ext}"))
    
    video_files = sorted(video_files)[:3]  # Limit to 3 for demo
    
    if not video_files:
        print(f"⚠️  No video files found in {video_dir}")
        return
    
    print(f"🎬 Processing {len(video_files)} video files...")
    
    query = "Provide a brief security assessment of this maritime footage."
    
    for i, video_file in enumerate(video_files, 1):
        print(f"\n   📹 Processing {i}/{len(video_files)}: {video_file.name}")
        
        output_json = f"output/{video_file.stem}_cosmos_batch.json"
        
        try:
            result = chat_with_media_nvcf(
                infer_url="https://ai.api.nvidia.com/v1/vlm/nvidia/cosmos-nemotron-34b",
                media_files=[str(video_file)],
                query=query,
                stream=False,
                save_json=output_json
            )
            
            print(f"      ✅ Success: {result['analysis_metadata']['processing_time_seconds']:.1f}s")
            print(f"      📝 Caption: {len(result['caption_text'])} chars")
            
        except Exception as e:
            print(f"      ❌ Failed: {e}")


def example_custom_query():
    """Example 5: Custom analysis query"""
    print("\n🎯 Example 5: Custom Query")
    
    video_file = "video_chunks/chunk_000.mp4"
    if not os.path.exists(video_file):
        print(f"⚠️  Video file not found: {video_file}")
        return
    
    # Custom query for detailed analysis
    custom_query = """
    You are an expert maritime analyst. Analyze this video and provide:
    
    1. VESSEL ANALYSIS:
       - Count and classify all visible vessels
       - Estimate sizes (small/medium/large)
       - Note any unusual vessel behavior
    
    2. ENVIRONMENTAL CONDITIONS:
       - Weather conditions
       - Sea state
       - Visibility
       - Time of day (if determinable)
    
    3. ACTIVITY ASSESSMENT:
       - Normal vs suspicious activities
       - Any safety concerns
       - Compliance with maritime regulations
    
    4. TRACKING INFORMATION:
       - Use any visible tracking IDs or bounding boxes
       - Note object persistence across frames
    
    Format your response with clear sections and bullet points.
    """
    
    result = chat_with_media_nvcf(
        infer_url="https://ai.api.nvidia.com/v1/vlm/nvidia/cosmos-nemotron-34b",
        media_files=[video_file],
        query=custom_query,
        stream=False,
        save_json="output/detailed_analysis.json"
    )
    
    print(f"✅ Detailed analysis completed")
    print(f"📊 Processing time: {result['analysis_metadata']['processing_time_seconds']:.2f}s")
    print(f"📝 Analysis length: {len(result['caption_text'])} characters")
    print("\n📋 Analysis Preview:")
    print(result['caption_text'][:500] + "..." if len(result['caption_text']) > 500 else result['caption_text'])


def main():
    """Run all examples"""
    print("🚀 Cosmos Nemotron Analysis Examples")
    print("=" * 50)
    
    # Check API key
    if not os.getenv("TEST_NVCF_API_KEY"):
        print("❌ Error: TEST_NVCF_API_KEY environment variable is required")
        print("   Export your NVIDIA API key: export TEST_NVCF_API_KEY=your_key_here")
        return
    
    # Create output directory
    os.makedirs("output", exist_ok=True)
    
    # Run examples
    try:
        example_basic_analysis()
        example_save_to_json()
        example_save_to_database()
        example_batch_processing()
        example_custom_query()
        
        print("\n🎉 All examples completed!")
        print("📁 Check the 'output' directory for saved results")
        
    except Exception as e:
        print(f"❌ Example failed: {e}")


if __name__ == "__main__":
    main()
