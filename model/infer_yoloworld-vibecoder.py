import argparse
import json
import logging
import os
import re

from pathlib import Path
from ultralytics import YOL<PERSON>
from ultralytics.data.loaders import get_best_youtube_url
import numpy as np

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

TRACK_OUTPUT_DIR="/home/<USER>/gitlab/demo-vss/model/output_dir"

# Custom type function to convert comma-separated string to list
def csv_to_list(string):
    return string.split(',')

def select_model(model_name):
    match model_name:
        case 'yoloworldx':
            return YOLO("data/weights/yolov8x-worldv2.pt")
        case 'yoloworldm':
            return YOLO("data/weights/yolov8m-worldv2.pt")
        case 'yoloworldl':
            return YOLO("data/weights/yolov8l-worldv2.pt")
        case 'yoloes':
            return YOLO("data/weights/yoloe-11s-seg.pt")
        case 'yoloem':
            return YOLO("data/weights/yoloe-11m-seg.pt")
        case 'yoloel':
            return YOLO("data/weights/yoloe-11l-seg.pt")

def natural_sort_key(s):
    # Extract numbers and strings for natural sorting
    return [int(text) if text.isdigit() else text.lower() for text in re.split(r'(\d+)', s)]

def list_and_sort_videos(directory):
    video_extensions = ('.mp4', '.avi', '.mov', '.mkv')
    p = Path(directory)
    # List all video files
    video_files = [str(f) for f in p.rglob('*') if f.suffix.lower() in video_extensions]
    # Sort by natural sort key on filename only, ignoring the path
    video_files.sort(key=lambda x: natural_sort_key(os.path.basename(x)))
    return video_files

def results_to_json(results, output_path, source_name=None):
    """
    Convert ultralytics results to JSON format with bounding boxes, masks, and probabilities

    Args:
        results: Single result object or list of results from model.predict() or model.track()
        output_path: Path to save JSON file
        source_name: Optional source video/image name for metadata

    Returns:
        output_data: The JSON data structure
    """
    output_data = {
        "source": source_name,
        "frames": []
    }

    # Handle both single result and list of results
    if not isinstance(results, list):
        results = [results]

    for frame_idx, result in enumerate(results):
        frame_data = {
            "frame_index": frame_idx,
            "image_shape": {
                "height": int(result.orig_shape[0]),
                "width": int(result.orig_shape[1])
            },
            "detections": []
        }

        # Extract boxes and other detection data
        if result.boxes is not None and len(result.boxes) > 0:
            boxes = result.boxes

            for i in range(len(boxes)):
                detection = {
                    "class_id": int(boxes.cls[i]),
                    "class_name": result.names[int(boxes.cls[i])],
                    "confidence": round(float(boxes.conf[i]), 4),
                    "bbox": {
                        "x1": round(float(boxes.xyxy[i][0]), 2),
                        "y1": round(float(boxes.xyxy[i][1]), 2),
                        "x2": round(float(boxes.xyxy[i][2]), 2),
                        "y2": round(float(boxes.xyxy[i][3]), 2)
                    },
                    "bbox_normalized": {
                        "x1": round(float(boxes.xyxyn[i][0]), 4),
                        "y1": round(float(boxes.xyxyn[i][1]), 4),
                        "x2": round(float(boxes.xyxyn[i][2]), 4),
                        "y2": round(float(boxes.xyxyn[i][3]), 4)
                    }
                }

                # Add tracking ID if available (for tracking tasks)
                if hasattr(boxes, 'id') and boxes.id is not None and len(boxes.id) > i:
                    detection["track_id"] = int(boxes.id[i])

                # Add mask if available (for segmentation models)
                if result.masks is not None and i < len(result.masks):
                    try:
                        mask = result.masks.data[i].cpu().numpy()
                        # Store mask metadata and optionally the mask data
                        detection["mask"] = {
                            "shape": list(mask.shape),
                            "has_mask": True
                            # Uncomment below to save full mask data (warning: large file size)
                            # "mask_data": mask.astype(int).tolist()
                        }

                        # Calculate mask area (number of pixels)
                        detection["mask"]["area"] = int(np.sum(mask > 0))
                    except Exception as e:
                        logger.warning(f"Failed to extract mask for detection {i}: {e}")
                        detection["mask"] = {"has_mask": False}

                frame_data["detections"].append(detection)

        # Add frame statistics
        frame_data["num_detections"] = len(frame_data["detections"])
        output_data["frames"].append(frame_data)

    # Add summary statistics
    output_data["total_frames"] = len(output_data["frames"])
    output_data["total_detections"] = sum(f["num_detections"] for f in output_data["frames"])

    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Save to JSON
    with open(output_path, 'w') as f:
        json.dump(output_data, f, indent=2)

    logger.info(f"Saved results to {output_path}")
    logger.info(f"Total frames: {output_data['total_frames']}, Total detections: {output_data['total_detections']}")

    return output_data


def main():
    parser = argparse.ArgumentParser(description="Run OVD on videos")
    parser.add_argument('--src', type=str, required=True, help='Video directory path')
    parser.add_argument('--model', type=str, required=True, help='yoloe, yoloworld, gdino, florence')
    parser.add_argument('--prompt', type=str, required=True, help='List of things that you want to detect. Eg. helmet, boot, ..')
    parser.add_argument('--task', type=str, default='detect', help='Types of task to perform: detect, segment, track')
    parser.add_argument('--output', type=str, default='output', help='Output directory for JSON results')
    parser.add_argument('--save-json', action='store_true', help='Save detection results to JSON files')

    args = parser.parse_args()

    if args.src.startswith("https://www.youtube.com"):
        source_url = get_best_youtube_url(args.src)
    else:
        # Assume the source is either a file or directory
        if os.path.isdir(args.src):
            # Search all video files in the directory and create a list
            source_url = list_and_sort_videos(args.src)
        elif os.path.isfile(args.src):
            source_url = args.src
        else:
            logger.error(f"{args.src}Invalid path or filename")
            return -1

    # Select model
    model = select_model(args.model)

    # Set prompt/classes
    model.set_classes(csv_to_list(args.prompt))


    if args.task == "detect":
        if isinstance(source_url, list):
            for source in source_url:
                results = model.predict(source=source, show=True)

                # Save results to JSON if requested
                if args.save_json:
                    source_name = Path(source).stem
                    output_path = os.path.join(args.output, f"{source_name}_results.json")
                    results_to_json(results, output_path, source_name=source)
        else:
            results = model.predict(source=source_url, show=True)

            # Save results to JSON if requested
            if args.save_json:
                source_name = Path(source_url).stem if not source_url.startswith("http") else "youtube_video"
                output_path = os.path.join(args.output, f"{source_name}_results.json")
                results_to_json(results, output_path, source_name=source_url)

    elif args.task == "track":
        if isinstance(source_url, list):
            for source in source_url:
                results = model.track(source=source, vid_stride=5, show=True, stream=False)

                # Save results to JSON if requested
                if args.save_json:
                    source_name = Path(source).stem
                    output_path = os.path.join(args.output, f"{source_name}_track_results.json")
                    results_to_json(results, output_path, source_name=source)
        else:
            results = model.track(source=source_url, save=True)

            # Save results to JSON if requested
            if args.save_json:
                source_name = Path(source_url).stem if not source_url.startswith("http") else "youtube_video"
                output_path = os.path.join(args.output, f"{source_name}_track_results.json")
                # For streaming results, we need to collect them
                results_list = list(results) if hasattr(results, '__iter__') else results
                results_to_json(results_list, output_path, source_name=source_url)


#youtube_url = "https://www.youtube.com/watch?v=HdwltphnqL4"
#stream_url = get_best_youtube_url(youtube_url)


if __name__ == "__main__":
    main()
