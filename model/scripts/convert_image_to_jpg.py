import os
import argparse
from PIL import Image

def convert_image_to_jpg(input_path):
    try:
        with Image.open(input_path) as img:
            rgb_img = img.convert('RGB')
            base_name = os.path.splitext(os.path.basename(input_path))[0]
            dir_name = os.path.dirname(input_path)
            output_path = os.path.join(dir_name, f"{base_name}.jpg")
            rgb_img.save(output_path, "JPEG")
            print(f"Converted: {input_path} -> {output_path}")
    except Exception as e:
        print(f"Failed to convert {input_path}: {e}")

def process_folder(folder_path):
    for root, _, files in os.walk(folder_path):
        for file in files:
            input_file = os.path.join(root, file)
            convert_image_to_jpg(input_file)

def main():
    parser = argparse.ArgumentParser(description="Convert images to JPG format.")
    parser.add_argument("--input", help="Input image file or folder")
    args = parser.parse_args()

    if os.path.isfile(args.input):
        convert_image_to_jpg(args.input)
    elif os.path.isdir(args.input):
        process_folder(args.input)
    else:
        print(f"Input path {args.input} is not a valid file or directory.")

if __name__ == "__main__":
    main()