"""Compare weights of 2 YOLO models to see which layers differ."""

import argparse
import torch
from ultralytics import <PERSON><PERSON><PERSON>

def compare_weights(ckpt1, ckpt2):
    model1 = YOLO(ckpt1)
    model2 = YOLO(ckpt2)

    state_dict1 = model1.model.state_dict()
    state_dict2 = model2.model.state_dict()

    keys1 = set(state_dict1.keys())
    keys2 = set(state_dict2.keys())

    all_keys = keys1.union(keys2)
    for key in sorted(all_keys):
        if key not in state_dict1:
            print(f"{key} only in {ckpt2}")
            continue
        if key not in state_dict2:
            print(f"{key} only in {ckpt1}")
            continue

        w1 = state_dict1[key]
        w2 = state_dict2[key]
        if not torch.equal(w1, w2):
            print(f"{key}: weights differ")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Compare weights of two .pt files")
    parser.add_argument("--ckpt1", type=str, help="First .pt file")
    parser.add_argument("--ckpt2", type=str, help="Second .pt file")
    args = parser.parse_args()
    compare_weights(args.ckpt1, args.ckpt2)