# Enhanced Cosmos Nemotron Analysis

Enhanced version of the Cosmos Nemotron script with JSON output and database integration capabilities.

## 🚀 Features

- **JSON Output**: Save analysis results to structured JSON files
- **Database Integration**: Store captions directly in your video analysis database
- **Batch Processing**: Process multiple video chunks automatically
- **Flexible Queries**: Customize analysis prompts for different use cases
- **Comprehensive Metadata**: Track processing times, model parameters, and file information
- **Error Handling**: Robust error handling with detailed logging

## 📋 Requirements

### Environment Variables
```bash
export TEST_NVCF_API_KEY=your_nvidia_api_key_here
```

### Python Dependencies
```bash
pip install requests psycopg2-binary  # For database integration (optional)
```

### Database Setup (Optional)
If you want to store results in the database, set up the hybrid video analysis database first:
```bash
# See database/HYBRID_README.md for setup instructions
```

## 🎯 Usage Examples

### 1. Basic Analysis (Console Output)
```bash
python cosmos-nemotron.py video_chunks/chunk_000.mp4
```

### 2. Save to JSON File
```bash
python cosmos-nemotron.py video_chunks/chunk_000.mp4 --save-json output/analysis.json
```

### 3. Save to Database
```bash
python cosmos-nemotron.py video_chunks/chunk_000.mp4 \
  --save-db --db-connection "postgresql://user:pass@localhost/video_analysis"
```

### 4. Process Directory of Videos
```bash
python cosmos-nemotron.py video_chunks/ --save-json output/batch_analysis.json
```

### 5. Custom Query
```bash
python cosmos-nemotron.py chunk.mp4 \
  --query "Describe the maritime activity and any safety concerns in this video" \
  --save-json output/custom_analysis.json
```

### 6. Batch Processing with Database Storage
```bash
python batch_cosmos_analysis.py video_chunks/ \
  --output output/cosmos_results/ \
  --db-connection "postgresql://user:pass@localhost/video_analysis" \
  --create-report
```

## 📊 Output Formats

### JSON Structure
```json
{
  "analysis_metadata": {
    "timestamp": "2024-01-15T10:30:00",
    "processing_time_seconds": 12.34,
    "model_name": "nvidia/cosmos-nemotron-34b",
    "model_version": "34b",
    "query": "Your analysis query...",
    "parameters": {
      "max_tokens": 1024,
      "temperature": 0.2,
      "top_p": 0.7,
      "seed": 50,
      "num_frames_per_inference": 8
    }
  },
  "media_files": [
    {
      "file_path": "video_chunks/chunk_000.mp4",
      "file_name": "chunk_000.mp4",
      "file_type": "mp4",
      "media_type": "video",
      "asset_id": "uuid-here"
    }
  ],
  "analysis_result": {
    "choices": [
      {
        "message": {
          "content": "Analysis result from Cosmos Nemotron..."
        }
      }
    ]
  },
  "caption_text": "Extracted caption text...",
  "confidence_score": null
}
```

### Database Storage
When using `--save-db`, the script stores captions in the `dense_captions` table:
- **video_chunk_id**: Links to existing video chunk record
- **caption_text**: The analysis result text
- **model_name**: "nvidia/cosmos-nemotron-34b"
- **processing_time_seconds**: Time taken for analysis
- **caption_type**: "cosmos_nemotron"

## 🔧 Command Line Options

### cosmos-nemotron.py
```
positional arguments:
  media_files           Media files or directories to analyze

optional arguments:
  --save-json PATH      Save analysis result to JSON file
  --save-db             Save analysis result to database
  --db-connection STR   Database connection string (required if --save-db)
  --query STR           Custom query for the model
  --stream              Use streaming response
  --output-dir DIR      Output directory for JSON files (default: output)
```

### batch_cosmos_analysis.py
```
positional arguments:
  video_chunks_dir      Directory containing video chunks to process

optional arguments:
  --output DIR          Output directory for results
  --db-connection STR   Database connection string
  --query STR           Custom query for analysis
  --create-report       Create a human-readable analysis report
```

## 🎬 Integration with Video Pipeline

### Step 1: Process Video with YOLO
```bash
# First, run YOLO analysis to create database records
python model/infer_yoloworld-vibecoder.py \
  --src video_chunks/ \
  --model yoloworldx \
  --prompt "boat,cat,person" \
  --task track \
  --save-json
```

### Step 2: Add Dense Captions with Cosmos Nemotron
```bash
# Then add dense captions using Cosmos Nemotron
python batch_cosmos_analysis.py video_chunks/ \
  --output output/cosmos_results/ \
  --db-connection "postgresql://user:pass@localhost/video_analysis"
```

### Step 3: Query Combined Results
```python
from database.hybrid_video_db import HybridVideoAnalysisDB

with HybridVideoAnalysisDB(connection_string) as db:
    # Search captions
    results = db.search_captions("boat suspicious activity")
    
    # Get video summary with both detections and captions
    summary = db.get_video_summary(video_id)
```

## 📈 Batch Processing Features

The `batch_cosmos_analysis.py` script provides:

- **Automatic Processing**: Processes all video chunks in a directory
- **Progress Tracking**: Shows progress and handles failures gracefully
- **Summary Reports**: Creates detailed batch processing reports
- **Database Integration**: Automatically stores all results in database
- **Error Recovery**: Continues processing even if individual files fail

### Batch Output Structure
```
output/cosmos_results/
├── chunk_000_cosmos_analysis.json
├── chunk_001_cosmos_analysis.json
├── chunk_002_cosmos_analysis.json
├── batch_summary.json
└── analysis_report.md
```

## 🔍 Query Templates

### Coastal Monitoring (Default)
```
You are a police coast guard monitoring the coastal and sea. The footage provided is the view of the sea in thermal images. You must monitor and take note of any presence of person or illegal boat activity. Start each event description with a start and end time stamp of the event, and use the person's tracking ID and boat tracking ID on the video in the description. Do not hallucinate and only report what you see in the video especially those that has been detected by the bounding box.
```

### General Maritime Analysis
```
Analyze this maritime surveillance footage and describe:
1. All visible vessels (type, size, behavior)
2. Any people or human activity
3. Environmental conditions
4. Unusual or suspicious activities
5. Safety concerns or violations
```

### Detailed Security Assessment
```
You are a maritime security analyst. Provide a comprehensive assessment including:
- Vessel identification and classification
- Activity patterns and behaviors
- Compliance with maritime regulations
- Environmental and weather conditions
- Risk assessment and recommendations
```

## 🛠️ Troubleshooting

### Common Issues

1. **API Key Error**
   ```
   Solution: Export your NVIDIA API key
   export TEST_NVCF_API_KEY=your_key_here
   ```

2. **Database Connection Failed**
   ```
   Solution: Check connection string and ensure database is running
   postgresql://user:password@host:port/database
   ```

3. **Video Chunk Not Found in Database**
   ```
   Solution: Run YOLO analysis first to create chunk records
   python model/infer_yoloworld-vibecoder.py --src video_chunks/ --save-json
   ```

4. **File Not Found**
   ```
   Solution: Check file paths and ensure video files exist
   ls -la video_chunks/
   ```

### Performance Tips

- **Batch Processing**: Use `batch_cosmos_analysis.py` for multiple files
- **Database Indexing**: Ensure database indexes are created for fast queries
- **JSON Storage**: Use JSON files for archival and later analysis
- **Error Handling**: Check batch summary for failed analyses

## 📚 Examples

See `cosmos_example.py` for comprehensive usage examples including:
- Basic analysis
- JSON output
- Database storage
- Batch processing
- Custom queries

Run the examples:
```bash
python cosmos_example.py
```

This will demonstrate all features and create sample output files in the `output/` directory.
