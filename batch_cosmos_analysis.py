#!/usr/bin/env python3
"""
Batch Cosmos Nemotron Analysis Script
Processes multiple video chunks and stores results in database and JSON files
"""

import os
import sys
import json
import argparse
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# Import the enhanced cosmos nemotron module
from cosmos_nemotron import chat_with_media_nvcf, list_and_sort_videos

# Import database interface if available
try:
    from database.hybrid_video_db import HybridVideoAnalysisDB
    DB_AVAILABLE = True
except ImportError:
    print("Warning: Database interface not available")
    DB_AVAILABLE = False


def process_video_chunks_batch(video_chunks_dir: str, output_dir: str, 
                              db_connection: str = None, 
                              query: str = None) -> Dict[str, Any]:
    """
    Process all video chunks in a directory with Cosmos Nemotron
    
    Args:
        video_chunks_dir: Directory containing video chunks
        output_dir: Directory to save JSON results
        db_connection: Database connection string (optional)
        query: Custom query for analysis
    
    Returns:
        Summary of batch processing results
    """
    
    # Default query for coastal monitoring
    if not query:
        query = """You are a police coast guard monitoring the coastal and sea. The footage provided is the view of the sea in thermal images. You must monitor and take note of any presence of person or illegal boat activity. Start each event description with a start and end time stamp of the event, and use the person's tracking ID and boat tracking ID on the video in the description. Do not hallucinate and only report what you see in the video especially those that has been detected by the bounding box. Any queries not related to the monitoring of coastal and see shall be ignored."""
    
    # Get all video files
    video_files = list_and_sort_videos(video_chunks_dir)
    if not video_files:
        print(f"❌ No video files found in {video_chunks_dir}")
        return {"error": "No video files found"}
    
    print(f"🎬 Found {len(video_files)} video chunks to process")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Process each video chunk
    results = []
    successful_analyses = 0
    failed_analyses = 0
    
    for i, video_file in enumerate(video_files, 1):
        chunk_name = Path(video_file).stem
        print(f"\n📹 Processing {i}/{len(video_files)}: {chunk_name}")
        
        try:
            # Generate output paths
            json_output = os.path.join(output_dir, f"{chunk_name}_cosmos_analysis.json")
            
            # Run Cosmos Nemotron analysis
            result = chat_with_media_nvcf(
                infer_url="https://ai.api.nvidia.com/v1/vlm/nvidia/cosmos-nemotron-34b",
                media_files=[video_file],
                query=query,
                stream=False,
                save_json=json_output,
                save_to_db=bool(db_connection),
                db_connection=db_connection
            )
            
            # Add to results summary
            chunk_result = {
                "chunk_name": chunk_name,
                "video_file": video_file,
                "json_output": json_output,
                "processing_time": result["analysis_metadata"]["processing_time_seconds"],
                "caption_length": len(result["caption_text"]),
                "status": "success",
                "timestamp": result["analysis_metadata"]["timestamp"]
            }
            
            results.append(chunk_result)
            successful_analyses += 1
            
            print(f"   ✅ Success - Caption: {len(result['caption_text'])} chars, "
                  f"Time: {result['analysis_metadata']['processing_time_seconds']:.1f}s")
            
        except Exception as e:
            print(f"   ❌ Failed: {e}")
            chunk_result = {
                "chunk_name": chunk_name,
                "video_file": video_file,
                "status": "failed",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            results.append(chunk_result)
            failed_analyses += 1
    
    # Create batch summary
    batch_summary = {
        "batch_metadata": {
            "timestamp": datetime.now().isoformat(),
            "video_chunks_dir": video_chunks_dir,
            "output_dir": output_dir,
            "total_chunks": len(video_files),
            "successful_analyses": successful_analyses,
            "failed_analyses": failed_analyses,
            "success_rate": successful_analyses / len(video_files) if video_files else 0,
            "query_used": query
        },
        "results": results
    }
    
    # Save batch summary
    summary_path = os.path.join(output_dir, "batch_summary.json")
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(batch_summary, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 Batch Processing Complete!")
    print(f"   ✅ Successful: {successful_analyses}/{len(video_files)}")
    print(f"   ❌ Failed: {failed_analyses}/{len(video_files)}")
    print(f"   📁 Results saved to: {output_dir}")
    print(f"   📋 Summary saved to: {summary_path}")
    
    return batch_summary


def create_analysis_report(batch_summary: Dict, output_path: str):
    """Create a human-readable analysis report"""
    
    report_lines = []
    report_lines.append("# Cosmos Nemotron Batch Analysis Report")
    report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append("")
    
    # Summary statistics
    metadata = batch_summary["batch_metadata"]
    report_lines.append("## Summary")
    report_lines.append(f"- **Total Chunks Processed**: {metadata['total_chunks']}")
    report_lines.append(f"- **Successful Analyses**: {metadata['successful_analyses']}")
    report_lines.append(f"- **Failed Analyses**: {metadata['failed_analyses']}")
    report_lines.append(f"- **Success Rate**: {metadata['success_rate']:.1%}")
    report_lines.append(f"- **Input Directory**: {metadata['video_chunks_dir']}")
    report_lines.append(f"- **Output Directory**: {metadata['output_dir']}")
    report_lines.append("")
    
    # Successful analyses details
    successful_results = [r for r in batch_summary["results"] if r["status"] == "success"]
    if successful_results:
        report_lines.append("## Successful Analyses")
        
        # Calculate statistics
        total_time = sum(r["processing_time"] for r in successful_results)
        avg_time = total_time / len(successful_results)
        avg_caption_length = sum(r["caption_length"] for r in successful_results) / len(successful_results)
        
        report_lines.append(f"- **Average Processing Time**: {avg_time:.2f} seconds")
        report_lines.append(f"- **Total Processing Time**: {total_time:.2f} seconds")
        report_lines.append(f"- **Average Caption Length**: {avg_caption_length:.0f} characters")
        report_lines.append("")
        
        report_lines.append("### Individual Results")
        for result in successful_results:
            report_lines.append(f"- **{result['chunk_name']}**: "
                              f"{result['processing_time']:.1f}s, "
                              f"{result['caption_length']} chars")
    
    # Failed analyses
    failed_results = [r for r in batch_summary["results"] if r["status"] == "failed"]
    if failed_results:
        report_lines.append("")
        report_lines.append("## Failed Analyses")
        for result in failed_results:
            report_lines.append(f"- **{result['chunk_name']}**: {result.get('error', 'Unknown error')}")
    
    # Query used
    report_lines.append("")
    report_lines.append("## Query Used")
    report_lines.append("```")
    report_lines.append(metadata['query_used'])
    report_lines.append("```")
    
    # Save report
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))
    
    print(f"📄 Analysis report saved to: {output_path}")


def main():
    """Main function for batch processing"""
    parser = argparse.ArgumentParser(
        description="Batch process video chunks with Cosmos Nemotron",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process all chunks in directory
  python batch_cosmos_analysis.py video_chunks/ --output output/cosmos_results/
  
  # Process with database storage
  python batch_cosmos_analysis.py video_chunks/ --output output/cosmos_results/ \\
    --db-connection "postgresql://user:pass@localhost/video_analysis"
  
  # Custom query
  python batch_cosmos_analysis.py video_chunks/ --output output/ \\
    --query "Describe the maritime activity in this video"

Environment Variables:
  TEST_NVCF_API_KEY: Required NVIDIA API key
        """
    )
    
    parser.add_argument('video_chunks_dir', 
                       help='Directory containing video chunks to process')
    parser.add_argument('--output', '-o', type=str, required=True,
                       help='Output directory for results')
    parser.add_argument('--db-connection', type=str,
                       help='Database connection string for storing results')
    parser.add_argument('--query', type=str,
                       help='Custom query for analysis (uses default coastal monitoring query if not specified)')
    parser.add_argument('--create-report', action='store_true',
                       help='Create a human-readable analysis report')
    
    args = parser.parse_args()
    
    # Validate inputs
    if not os.path.isdir(args.video_chunks_dir):
        print(f"❌ Error: {args.video_chunks_dir} is not a valid directory")
        sys.exit(1)
    
    if not os.getenv("TEST_NVCF_API_KEY"):
        print("❌ Error: TEST_NVCF_API_KEY environment variable is required")
        sys.exit(1)
    
    # Run batch processing
    try:
        batch_summary = process_video_chunks_batch(
            video_chunks_dir=args.video_chunks_dir,
            output_dir=args.output,
            db_connection=args.db_connection,
            query=args.query
        )
        
        # Create analysis report if requested
        if args.create_report:
            report_path = os.path.join(args.output, "analysis_report.md")
            create_analysis_report(batch_summary, report_path)
        
        print(f"\n🎉 Batch processing completed successfully!")
        
    except Exception as e:
        print(f"❌ Batch processing failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
